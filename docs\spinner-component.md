# Componente Spinner Reutilizable

## Descripción
Componente spinner reutilizable para mostrar indicadores de carga durante procesos como login, envío de formularios, etc.

## Archivos
- `src/components/spinner.ejs` - Componente HTML y CSS
- `public/wwwroot/components/spinner.js` - Funciones JavaScript para controlar el spinner

## Uso

### 1. Incluir el componente en una vista EJS

```ejs
<!-- Incluir el componente -->
<%- include('../components/spinner.ejs') %>

<!-- Incluir el script JavaScript -->
<script src="/wwwroot/components/spinner.js"></script>
```

### 2. Funciones JavaScript disponibles

#### Mostrar spinner
```javascript
// Mostrar con mensaje por defecto
showSpinner();

// Mostrar con mensaje personalizado
showSpinner('Verificando credenciales...');
```

#### Ocultar spinner
```javascript
// Ocultar inmediatamente
hideSpinner();

// Ocultar con retraso (en milisegundos)
hideSpinner(1000);
```

#### Actualizar mensaje
```javascript
// Cambiar el mensaje sin ocultar el spinner
updateSpinnerMessage('Procesando autenticación...');
```

#### Verificar estado
```javascript
// Verificar si el spinner está visible
if (isSpinnerShowing()) {
    console.log('Spinner está visible');
}
```

## Ejemplo de uso en login

```javascript
$('form[action="/login"]').on('submit', function(e) {
    e.preventDefault();
    
    // Mostrar spinner
    showSpinner('Verificando credenciales...');
    
    $.ajax({
        url: '/login',
        method: 'POST',
        data: formData,
        success: function(response) {
            updateSpinnerMessage('Acceso concedido, redirigiendo...');
            setTimeout(() => {
                window.location.href = '/home';
            }, 800);
        },
        error: function() {
            hideSpinner();
            // Mostrar mensaje de error
        }
    });
});
```

## Personalización

### Cambiar colores
Editar el archivo `src/components/spinner.ejs` y modificar:
- `border-top-color: #26c6da;` - Color del spinner
- `background: rgba(0, 0, 0, 0.7);` - Color de fondo

### Cambiar tamaño
Modificar las propiedades `width` y `height` de `.loading-spinner-circle`

## Características
- ✅ Reutilizable en cualquier vista
- ✅ Mensajes personalizables
- ✅ Animaciones suaves
- ✅ Responsive
- ✅ Evita conflictos con otros spinners
- ✅ Bloquea interacción del usuario durante la carga
