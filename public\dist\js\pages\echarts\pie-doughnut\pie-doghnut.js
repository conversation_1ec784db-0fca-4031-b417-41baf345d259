$(function () {
  "use strict";
  // ------------------------------
  // Basic pie chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var basicpieChart = echarts.init(document.getElementById("basic-pie"));
  var option = {
    // Add title
    title: {
      text: "Browser popularity",
      subtext: "Open source information",
      x: "center",
    },

    // Add tooltip
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },

    // Add legend
    legend: {
      orient: "vertical",
      x: "left",
      data: ["IE", "Opera", "Safari", "Firefox", "Chrome"],
    },

    // Add custom colors
    color: ["#ffbc34", "#00acc1", "#212529", "#f62d51", "#1e88e5"],

    // Display toolbox
    toolbox: {
      show: true,
      orient: "vertical",
      feature: {
        mark: {
          show: true,
          title: {
            mark: "Markline switch",
            markUndo: "Undo markline",
            markClear: "Clear markline",
          },
        },
        dataView: {
          show: true,
          readOnly: false,
          title: "View data",
          lang: ["View chart data", "Close", "Update"],
        },
        magicType: {
          show: true,
          title: {
            pie: "Switch to pies",
            funnel: "Switch to funnel",
          },
          type: ["pie", "funnel"],
          option: {
            funnel: {
              x: "25%",
              y: "20%",
              width: "50%",
              height: "70%",
              funnelAlign: "left",
              max: 1548,
            },
          },
        },
        restore: {
          show: true,
          title: "Restore",
        },
        saveAsImage: {
          show: true,
          title: "Same as image",
          lang: ["Save"],
        },
      },
    },

    // Enable drag recalculate
    calculable: true,

    // Add series
    series: [
      {
        name: "Browsers",
        type: "pie",
        radius: "70%",
        center: ["50%", "57.5%"],
        data: [
          { value: 335, name: "IE" },
          { value: 310, name: "Opera" },
          { value: 234, name: "Safari" },
          { value: 135, name: "Firefox" },
          { value: 1548, name: "Chrome" },
        ],
      },
    ],
  };

  basicpieChart.setOption(option);
  // ------------------------------
  // Basic pie chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var basicdoughnutChart = echarts.init(
    document.getElementById("basic-doughnut")
  );
  var option = {
    // Add title
    title: {
      text: "Browser popularity",
      subtext: "Open source information",
      x: "center",
    },

    // Add legend
    legend: {
      orient: "vertical",
      x: "left",
      data: ["Internet Explorer", "Opera", "Safari", "Firefox", "Chrome"],
    },

    // Add custom colors
    color: ["#ffbc34", "#00acc1", "#212529", "#f62d51", "#1e88e5"],

    // Display toolbox
    toolbox: {
      show: true,
      orient: "vertical",
      feature: {
        mark: {
          show: true,
          title: {
            mark: "Markline switch",
            markUndo: "Undo markline",
            markClear: "Clear markline",
          },
        },
        dataView: {
          show: true,
          readOnly: false,
          title: "View data",
          lang: ["View chart data", "Close", "Update"],
        },
        magicType: {
          show: true,
          title: {
            pie: "Switch to pies",
            funnel: "Switch to funnel",
          },
          type: ["pie", "funnel"],
          option: {
            funnel: {
              x: "25%",
              y: "20%",
              width: "50%",
              height: "70%",
              funnelAlign: "left",
              max: 1548,
            },
          },
        },
        restore: {
          show: true,
          title: "Restore",
        },
        saveAsImage: {
          show: true,
          title: "Same as image",
          lang: ["Save"],
        },
      },
    },

    // Enable drag recalculate
    calculable: true,

    // Add series
    series: [
      {
        name: "Browsers",
        type: "pie",
        radius: ["50%", "70%"],
        center: ["50%", "57.5%"],
        itemStyle: {
          normal: {
            label: {
              show: true,
            },
            labelLine: {
              show: true,
            },
          },
          emphasis: {
            label: {
              show: true,
              formatter: "{b}" + "\n\n" + "{c} ({d}%)",
              position: "center",
              textStyle: {
                fontSize: "17",
                fontWeight: "500",
              },
            },
          },
        },

        data: [
          { value: 335, name: "Internet Explorer" },
          { value: 310, name: "Opera" },
          { value: 234, name: "Safari" },
          { value: 135, name: "Firefox" },
          { value: 1548, name: "Chrome" },
        ],
      },
    ],
  };

  basicdoughnutChart.setOption(option);
  // ------------------------------
  // customized chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var customizedChart = echarts.init(
    document.getElementById("customized-chart")
  );
  var option = {
    backgroundColor: "#fff",

    title: {
      text: "Customized Pie",
      left: "center",
      top: 20,
      textStyle: {
        color: "#ccc",
      },
    },

    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b} : {c} ({d}%)",
    },

    visualMap: {
      show: false,
      min: 80,
      max: 600,
      inRange: {
        colorLightness: [0, 1],
      },
    },
    series: [
      {
        name: "Browsers",
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        data: [
          { value: 335, name: "Firefox" },
          { value: 310, name: "Safari" },
          { value: 274, name: "IE" },
          { value: 235, name: "Opera" },
          { value: 400, name: "Chrome" },
        ].sort(function (a, b) {
          return a.value - b.value;
        }),
        roseType: "radius",
        label: {
          normal: {
            textStyle: {
              color: "rgba(0, 0, 0, 0.3)",
            },
          },
        },
        labelLine: {
          normal: {
            lineStyle: {
              color: "rgba(0, 0, 0, 0.3)",
            },
            smooth: 0.2,
            length: 10,
            length2: 20,
          },
        },
        itemStyle: {
          normal: {
            color: "#1e88e5",
            shadowBlur: 200,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },

        animationType: "scale",
        animationEasing: "elasticOut",
        animationDelay: function (idx) {
          return Math.random() * 200;
        },
      },
    ],
  };

  customizedChart.setOption(option);
  // ------------------------------
  // Nested chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var nestedChart = echarts.init(document.getElementById("nested-pie"));
  var option = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },

    // Add legend
    legend: {
      orient: "vertical",
      x: "left",
      data: [
        "Italy",
        "Spain",
        "Austria",
        "Germany",
        "Poland",
        "Denmark",
        "Hungary",
        "Portugal",
        "France",
        "Netherlands",
      ],
    },

    // Add custom colors
    color: ["#ffbc34", "#00acc1", "#212529", "#f62d51", "#1e88e5"],

    // Display toolbox
    toolbox: {
      show: true,
      orient: "vertical",
      feature: {
        mark: {
          show: true,
          title: {
            mark: "Markline switch",
            markUndo: "Undo markline",
            markClear: "Clear markline",
          },
        },
        dataView: {
          show: true,
          readOnly: false,
          title: "View data",
          lang: ["View chart data", "Close", "Update"],
        },
        magicType: {
          show: true,
          title: {
            pie: "Switch to pies",
            funnel: "Switch to funnel",
          },
          type: ["pie", "funnel"],
        },
        restore: {
          show: true,
          title: "Restore",
        },
        saveAsImage: {
          show: true,
          title: "Same as image",
          lang: ["Save"],
        },
      },
    },

    // Enable drag recalculate
    calculable: false,

    // Add series
    series: [
      // Inner
      {
        name: "Countries",
        type: "pie",
        selectedMode: "single",
        radius: [0, "40%"],

        // for funnel
        x: "15%",
        y: "7.5%",
        width: "40%",
        height: "85%",
        funnelAlign: "right",
        max: 1548,

        itemStyle: {
          normal: {
            label: {
              position: "inner",
            },
            labelLine: {
              show: false,
            },
          },
          emphasis: {
            label: {
              show: true,
            },
          },
        },

        data: [
          { value: 535, name: "Italy" },
          { value: 679, name: "Spain" },
          { value: 1548, name: "Austria" },
        ],
      },

      // Outer
      {
        name: "Countries",
        type: "pie",
        radius: ["60%", "85%"],

        // for funnel
        x: "55%",
        y: "7.5%",
        width: "35%",
        height: "85%",
        funnelAlign: "left",
        max: 1048,

        data: [
          { value: 535, name: "Italy" },
          { value: 310, name: "Germany" },
          { value: 234, name: "Poland" },
          { value: 135, name: "Denmark" },
          { value: 948, name: "Hungary" },
          { value: 251, name: "Portugal" },
          { value: 147, name: "France" },
          { value: 202, name: "Netherlands" },
        ],
      },
    ],
  };

  nestedChart.setOption(option);
  // ------------------------------
  // pole chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var poleChart = echarts.init(document.getElementById("pole-chart"));
  // Data style
  var dataStyle = {
    normal: {
      label: { show: false },
      labelLine: { show: false },
    },
  };

  // Placeholder style
  var placeHolderStyle = {
    normal: {
      color: "rgba(0,0,0,0)",
      label: { show: false },
      labelLine: { show: false },
    },
    emphasis: {
      color: "rgba(0,0,0,0)",
    },
  };
  var option = {
    title: {
      text: "Are you Satisfied?",
      subtext: "Ahmedabad, India",
      x: "center",
      y: "center",
      itemGap: 10,
      textStyle: {
        color: "rgba(30,144,255,0.8)",
        fontSize: 19,
        fontWeight: "500",
      },
    },

    // Add tooltip
    tooltip: {
      show: true,
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },

    // Add legend
    legend: {
      orient: "vertical",
      x: document.getElementById("pole-chart").offsetWidth / 2,
      y: 30,
      x: "55%",
      itemGap: 15,
      data: [
        "60% Definitely yes",
        "30% Could be better",
        "10% Not at the moment",
      ],
    },

    // Add custom colors
    color: ["#1e88e5", "#00acc1", "#f62d51"],

    // Add series
    series: [
      {
        name: "1",
        type: "pie",
        clockWise: false,
        radius: ["75%", "90%"],
        itemStyle: dataStyle,
        data: [
          {
            value: 60,
            name: "60% Definitely yes",
          },
          {
            value: 40,
            name: "invisible",
            itemStyle: placeHolderStyle,
          },
        ],
      },

      {
        name: "2",
        type: "pie",
        clockWise: false,
        radius: ["60%", "75%"],
        itemStyle: dataStyle,
        data: [
          {
            value: 30,
            name: "30% Could be better",
          },
          {
            value: 70,
            name: "invisible",
            itemStyle: placeHolderStyle,
          },
        ],
      },

      {
        name: "3",
        type: "pie",
        clockWise: false,
        radius: ["45%", "60%"],
        itemStyle: dataStyle,
        data: [
          {
            value: 10,
            name: "10% Not at the moment",
          },
          {
            value: 90,
            name: "invisible",
            itemStyle: placeHolderStyle,
          },
        ],
      },
    ],
  };
  poleChart.setOption(option);
  // ------------------------------
  // nightingale chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var nightingaleChart = echarts.init(
    document.getElementById("nightingale-chart")
  );
  var option = {
    title: {
      text: "Employee's salary review",
      subtext: "Senior front end developer",
      x: "center",
    },

    // Add tooltip
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: +{c}$ ({d}%)",
    },

    // Add legend
    legend: {
      x: "left",
      y: "top",
      orient: "vertical",
      data: [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
      ],
    },

    color: [
      "#ffbc34",
      "#00acc1",
      "#212529",
      "#f62d51",
      "#1e88e5",
      "#FFC400",
      "#006064",
      "#FF1744",
      "#1565C0",
      "#FFC400",
      "#64FFDA",
      "#607D8B",
    ],

    // Display toolbox
    toolbox: {
      show: true,
      orient: "vertical",
      feature: {
        mark: {
          show: true,
          title: {
            mark: "Markline switch",
            markUndo: "Undo markline",
            markClear: "Clear markline",
          },
        },
        dataView: {
          show: true,
          readOnly: false,
          title: "View data",
          lang: ["View chart data", "Close", "Update"],
        },
        magicType: {
          show: true,
          title: {
            pie: "Switch to pies",
            funnel: "Switch to funnel",
          },
          type: ["pie", "funnel"],
        },
        restore: {
          show: true,
          title: "Restore",
        },
        saveAsImage: {
          show: true,
          title: "Same as image",
          lang: ["Save"],
        },
      },
    },

    // Enable drag recalculate
    calculable: true,

    // Add series
    series: [
      {
        name: "Increase (brutto)",
        type: "pie",
        radius: ["15%", "73%"],
        center: ["50%", "57%"],
        roseType: "area",

        // Funnel
        width: "40%",
        height: "78%",
        x: "30%",
        y: "17.5%",
        max: 450,
        sort: "ascending",

        data: [
          { value: 440, name: "January" },
          { value: 260, name: "February" },
          { value: 350, name: "March" },
          { value: 250, name: "April" },
          { value: 210, name: "May" },
          { value: 350, name: "June" },
          { value: 300, name: "July" },
          { value: 430, name: "August" },
          { value: 400, name: "September" },
          { value: 450, name: "October" },
          { value: 330, name: "November" },
          { value: 200, name: "December" },
        ],
      },
    ],
  };
  nightingaleChart.setOption(option);
  //------------------------------------------------------
  // Resize chart on menu width change and window resize
  //------------------------------------------------------
  $(function () {
    // Resize chart on menu width change and window resize
    $(window).on("resize", resize);
    $(".sidebartoggler").on("click", resize);

    // Resize function
    function resize() {
      setTimeout(function () {
        // Resize chart
        basicpieChart.resize();
        basicdoughnutChart.resize();
        customizedChart.resize();
        nestedChart.resize();
        poleChart.resize();
      }, 200);
    }
  });
});
