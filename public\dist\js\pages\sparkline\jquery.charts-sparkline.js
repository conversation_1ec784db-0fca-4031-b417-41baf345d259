$(document).ready(function () {
  var sparklineLogin = function () {
    $("#sparkline1").sparkline([5, 6, 2, 8, 9, 4, 7, 10, 11, 12, 10], {
      type: "bar",
      height: "45",
      barWidth: 7,
      barSpacing: 4,
      barColor: "#21c1d6",
    });

    $("#sparkline2").sparkline([20, 40, 30], {
      type: "pie",
      width: "50",
      height: "45",
      resize: true,
      sliceColors: ["#1e88e5", "#fc4b6c", "#f1f2f7"],
    });

    $("#sparkline3").sparkline([5, 6, 2, 9, 4, 7, 10, 12], {
      type: "bar",
      height: "164",
      barWidth: "7",
      resize: true,
      barSpacing: "5",
      barColor: "#1e88e5",
    });

    $("#sparkline4").sparkline(
      [0, 23, 43, 35, 44, 45, 56, 37, 40, 45, 56, 7, 10],
      {
        type: "line",
        width: "120",
        height: "45",
        lineColor: "#fb6d9d",
        fillColor: "transparent",
        spotColor: "#fb6d9d",
        minSpotColor: undefined,
        maxSpotColor: undefined,
        highlightSpotColor: undefined,
        highlightLineColor: undefined,
      }
    );

    $("#sparkline5").sparkline([15, 23, 55, 35, 54, 45, 66, 47, 30], {
      type: "line",
      width: "100%",
      height: "160",
      chartRangeMax: 50,
      resize: true,
      lineColor: "#1e88e5",
      fillColor: "rgba(19, 218, 254, 0.3)",
      highlightLineColor: "rgba(0,0,0,.1)",
      highlightSpotColor: "rgba(0,0,0,.2)",
    });

    $("#sparkline5").sparkline([0, 13, 10, 14, 15, 10, 18, 20, 0], {
      type: "line",
      width: "100%",
      height: "160",
      chartRangeMax: 40,
      lineColor: "#fc4b6c",
      fillColor: "rgba(97, 100, 193, 0.3)",
      composite: true,
      resize: true,
      highlightLineColor: "rgba(0,0,0,.1)",
      highlightSpotColor: "rgba(0,0,0,.2)",
    });
    $("#sparkline6").sparkline([5, 6, 2, 8, 9, 4, 7, 10, 11, 12, 10], {
      type: "bar",
      height: "45",
      barWidth: "7",
      barSpacing: "4",
      barColor: "#1e88e5",
    });
    $("#sparkline7").sparkline([0, 2, 8, 6, 8, 5, 6, 4, 8, 6, 4, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#ffca4a",
      fillColor: "#ffca4a",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#4f4f4f",
    });
    $("#sparkline8").sparkline([2, 4, 4, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#21c1d6",
      fillColor: "#21c1d6",
      maxSpotColor: "#21c1d6",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#21c1d6",
    });
    $("#sparkline9").sparkline([0, 2, 8, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#1e88e5",
      fillColor: "#1e88e5",
      minSpotColor: "#1e88e5",
      maxSpotColor: "#1e88e5",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#1e88e5",
    });
    $("#sparkline10").sparkline([2, 4, 4, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#fc4b6c",
      fillColor: "#fc4b6c",
      maxSpotColor: "#fc4b6c",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#fc4b6c",
    });
    $("#sparkline11").sparkline([20, 40, 30], {
      type: "pie",
      height: "200",
      resize: true,
      sliceColors: ["#1e88e5", "#fc4b6c", "#f1f2f7"],
    });

    $("#sparkline12").sparkline(
      [5, 6, 2, 8, 9, 4, 7, 10, 11, 12, 10, 4, 7, 10],
      {
        type: "bar",
        height: "200",
        barWidth: 10,
        barSpacing: 7,
        barColor: "#21c1d6",
      }
    );

    $("#sparkline13").sparkline([5, 6, 2, 9, 4, 7, 10, 12, 4, 7, 10], {
      type: "bar",
      height: "200",
      barWidth: "10",
      resize: true,
      barSpacing: "7",
      barColor: "#1e88e5",
    });
    $("#sparkline13").sparkline([5, 6, 2, 9, 4, 7, 10, 12, 4, 7, 10], {
      type: "line",
      height: "200",
      lineColor: "#1e88e5",
      fillColor: "transparent",
      composite: true,
      highlightLineColor: "rgba(0,0,0,.1)",
      highlightSpotColor: "rgba(0,0,0,.2)",
    });
    $("#sparkline14").sparkline(
      [0, 23, 43, 35, 44, 45, 56, 37, 40, 45, 56, 7, 10],
      {
        type: "line",
        width: "100%",
        height: "200",
        lineColor: "#fff",
        fillColor: "transparent",
        spotColor: "#fff",
        minSpotColor: undefined,
        maxSpotColor: undefined,
        highlightSpotColor: undefined,
        highlightLineColor: undefined,
      }
    );
    $("#sparkline15").sparkline(
      [5, 6, 2, 8, 9, 4, 7, 10, 11, 12, 10, 9, 4, 7],
      {
        type: "bar",
        height: "200",
        barWidth: "10",
        barSpacing: "10",
        barColor: "#1e88e5",
      }
    );
    $("#sparkline16").sparkline([15, 23, 55, 35, 54, 45, 66, 47, 30], {
      type: "line",
      width: "100%",
      height: "200",
      chartRangeMax: 50,
      resize: true,
      lineColor: "#1e88e5",
      fillColor: "rgba(19, 218, 254, 0.3)",
      highlightLineColor: "rgba(0,0,0,.1)",
      highlightSpotColor: "rgba(0,0,0,.2)",
    });

    $("#sparkline16").sparkline([0, 13, 10, 14, 15, 10, 18, 20, 0], {
      type: "line",
      width: "100%",
      height: "200",
      chartRangeMax: 40,
      lineColor: "#fc4b6c",
      fillColor: "rgba(97, 100, 193, 0.3)",
      composite: true,
      resize: true,
      highlightLineColor: "rgba(0,0,0,.1)",
      highlightSpotColor: "rgba(0,0,0,.2)",
    });

    $("#sparklinedash").sparkline([0, 5, 6, 10, 9, 12, 4, 9], {
      type: "bar",
      height: "30",
      barWidth: "4",
      resize: true,
      barSpacing: "5",
      barColor: "#21c1d6",
    });
    $("#sparklinedash2").sparkline([0, 5, 6, 10, 9, 12, 4, 9], {
      type: "bar",
      height: "30",
      barWidth: "4",
      resize: true,
      barSpacing: "5",
      barColor: "#7460ee",
    });
    $("#sparklinedash3").sparkline([0, 5, 6, 10, 9, 12, 4, 9], {
      type: "bar",
      height: "30",
      barWidth: "4",
      resize: true,
      barSpacing: "5",
      barColor: "#1e88e5",
    });
    $("#sparklinedash4").sparkline([0, 5, 6, 10, 9, 12, 4, 9], {
      type: "bar",
      height: "30",
      barWidth: "4",
      resize: true,
      barSpacing: "5",
      barColor: "#fc4b6c",
    });
  };
  var sparkResize;

  $(window).resize(function (e) {
    clearTimeout(sparkResize);
    sparkResize = setTimeout(sparklineLogin, 500);
  });
  sparklineLogin();
});
