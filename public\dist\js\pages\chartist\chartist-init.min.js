$(function () {
  "use strict";
  new Chartist.Line(
    ".ct-sm-line-chart",
    {
      labels: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      series: [
        [12, 9, 7, 8, 5],
        [2, 1, 3.5, 7, 3],
        [1, 3, 4, 5, 6],
      ],
    },
    {
      fullWidth: !0,
      plugins: [Chartist.plugins.tooltip()],
      chartPadding: { right: 40 },
    }
  ),
    new Chartist.Line(
      ".ct-area-ln-chart",
      { labels: [1, 2, 3, 4, 5, 6, 7, 8], series: [[5, 9, 7, 8, 5, 3, 5, 4]] },
      { low: 0, plugins: [Chartist.plugins.tooltip()], showArea: !0 }
    ),
    new Chartist.Line(
      "#ct-polar-chart",
      {
        labels: [1, 2, 3, 4, 5, 6, 7, 8],
        series: [
          [1, 2, 3, 1, -2, 0, 1, 0],
          [-2, -1, -2, -1, -2.5, -1, -2, -1],
          [0, 0, 0, 1, 2, 2.5, 2, 1],
          [2.5, 2, 1, 0.5, 1, 0.5, -1, -2.5],
        ],
      },
      {
        high: 3,
        low: -3,
        chartPadding: { left: -20, top: 10 },
        showArea: !0,
        showLine: !1,
        showPoint: !0,
        fullWidth: !0,
        plugins: [Chartist.plugins.tooltip()],
        axisX: { showLabel: !0, showGrid: !0 },
        axisY: { showLabel: !1, showGrid: !0 },
      }
    );
  var e = new Chartist.Line(
      ".ct-animation-chart",
      {
        labels: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"],
        series: [
          [12, 9, 7, 8, 5, 4, 6, 2, 3, 3, 4, 6],
          [4, 5, 3, 7, 3, 5, 5, 3, 4, 4, 5, 5],
          [5, 3, 4, 5, 6, 3, 3, 4, 5, 6, 3, 4],
        ],
      },
      { low: 0 }
    ),
    n = 0,
    s = 80,
    r = 500;
  e.on("created", function () {
    n = 0;
  }),
    e.on("draw", function (e) {
      if ((n++, "line" === e.type))
        e.element.animate({
          opacity: { begin: n * s + 1e3, dur: r, from: 0, to: 1 },
        });
      else if ("label" === e.type && "x" === e.axis)
        e.element.animate({
          y: {
            begin: n * s,
            dur: r,
            from: e.y + 100,
            to: e.y,
            easing: "easeOutQuart",
          },
        });
      else if ("label" === e.type && "y" === e.axis)
        e.element.animate({
          x: {
            begin: n * s,
            dur: r,
            from: e.x - 100,
            to: e.x,
            easing: "easeOutQuart",
          },
        });
      else if ("point" === e.type)
        e.element.animate({
          x1: {
            begin: n * s,
            dur: r,
            from: e.x - 10,
            to: e.x,
            easing: "easeOutQuart",
          },
          x2: {
            begin: n * s,
            dur: r,
            from: e.x - 10,
            to: e.x,
            easing: "easeOutQuart",
          },
          opacity: {
            begin: n * s,
            dur: r,
            from: 0,
            to: 1,
            easing: "easeOutQuart",
          },
        });
      else if ("grid" === e.type) {
        var t = {
            begin: n * s,
            dur: r,
            from: e[e.axis.units.pos + "1"] - 30,
            to: e[e.axis.units.pos + "1"],
            easing: "easeOutQuart",
          },
          i = {
            begin: n * s,
            dur: r,
            from: e[e.axis.units.pos + "2"] - 100,
            to: e[e.axis.units.pos + "2"],
            easing: "easeOutQuart",
          },
          a = {};
        (a[e.axis.units.pos + "1"] = t),
          (a[e.axis.units.pos + "2"] = i),
          (a.opacity = {
            begin: n * s,
            dur: r,
            from: 0,
            to: 1,
            easing: "easeOutQuart",
          }),
          e.element.animate(a);
      }
    }),
    e.on("created", function () {
      window.__exampleAnimateTimeout &&
        (clearTimeout(window.__exampleAnimateTimeout),
        (window.__exampleAnimateTimeout = null)),
        (window.__exampleAnimateTimeout = setTimeout(e.update.bind(e), 12e3));
    }),
    (e = new Chartist.Line(
      ".ct-svg-chart",
      {
        labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
        series: [
          [1, 5, 2, 5, 4, 3],
          [2, 3, 4, 8, 1, 2],
          [5, 4, 3, 2, 1, 0.5],
        ],
      },
      { low: 0, showArea: !0, showPoint: !1, fullWidth: !0 }
    )).on("draw", function (e) {
      ("line" !== e.type && "area" !== e.type) ||
        e.element.animate({
          d: {
            begin: 2e3 * e.index,
            dur: 2e3,
            from: e.path
              .clone()
              .scale(1, 0)
              .translate(0, e.chartRect.height())
              .stringify(),
            to: e.path.clone().stringify(),
            easing: Chartist.Svg.Easing.easeOutQuint,
          },
        });
    });
  new Chartist.Bar(
    ".ct-bar-chart",
    {
      labels: [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
      ],
      series: [
        [5, 4, 3, 7, 5, 10, 3, 4, 8, 10, 6, 8],
        [3, 2, 9, 5, 4, 6, 4, 6, 7, 8, 7, 4],
      ],
    },
    { seriesBarDistance: 10 },
    [
      [
        "screen and (max-width: 640px)",
        {
          seriesBarDistance: 5,
          axisX: {
            labelInterpolationFnc: function (e) {
              return e[0];
            },
          },
        },
      ],
    ]
  ),
    new Chartist.Pie(
      ".ct-gauge-chart",
      { series: [20, 10, 30, 40] },
      {
        donut: !0,
        donutWidth: 60,
        startAngle: 270,
        total: 200,
        low: 0,
        showLabel: !1,
      }
    ),
    (e = new Chartist.Pie(
      ".ct-donute-chart",
      { series: [10, 20, 50, 20, 5, 50, 15], labels: [1, 2, 3, 4, 5, 6, 7] },
      { donut: !0, showLabel: !1 }
    )).on("draw", function (e) {
      if ("slice" === e.type) {
        var t = e.element._node.getTotalLength();
        e.element.attr({ "stroke-dasharray": t + "px " + t + "px" });
        var i = {
          "stroke-dashoffset": {
            id: "anim" + e.index,
            dur: 1e3,
            from: -t + "px",
            to: "0px",
            easing: Chartist.Svg.Easing.easeOutQuint,
            fill: "freeze",
          },
        };
        0 !== e.index &&
          (i["stroke-dashoffset"].begin = "anim" + (e.index - 1) + ".end"),
          e.element.attr({ "stroke-dashoffset": -t + "px" }),
          e.element.animate(i, !1);
      }
    }),
    e.on("created", function () {
      window.__anim21278907124 &&
        (clearTimeout(window.__anim21278907124),
        (window.__anim21278907124 = null)),
        (window.__anim21278907124 = setTimeout(e.update.bind(e), 1e4));
    });
});
