/*************************************************************************************/
// -->Template Name: Bootstrap Press Admin
// -->Author: Themedesigner
// -->Email: <EMAIL>
// -->File: c3_chart_JS
/*************************************************************************************/

/********************************/
// Line region chart            //
/********************************/
$(function () {
  // Callback that creates and populates a data table, instantiates the line region chart, passes in the data and draws it.
  var lineRegionChart = c3.generate({
    bindto: "#line-region",
    size: { height: 400 },
    point: {
      r: 4,
    },
    color: {
      pattern: ["#1e88e5", "#00acc1"],
    },

    // Create the data table.
    data: {
      columns: [
        ["data1", 30, 200, 100, 400, 0, 100],
        ["data2", 100, 200, 10, 400, 100, 25],
      ],
      regions: {
        data1: [{ start: 1, end: 2, style: "dashed" }, { start: 3 }], // currently 'dashed' style only
        data2: [{ end: 3 }],
      },
    },
    grid: {
      y: {
        show: true,
      },
    },
  });
});
