
/* Estilos para las iniciales del usuario en el sidebar */
.profile-picture {
    width: 50px;
    height: 50px;
    border-radius: 50%; /* Hace el círculo */
    background-color: #000; /* Color de fondo */
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    text-transform: uppercase;
}

#usuIniciales_topbar {
  background-color: #000 !important;
  border-radius: 50%;
  padding: 10px;
}

/* Efecto marquesina para nombres largos en el sidebar */
#dropdownMenuLink {
  display: block !important;
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  position: relative !important;
}

/* Contenedor interno para el texto que se mueve */
#dropdownMenuLink.text-overflow .marquee-text {
  display: inline-block !important;
  animation: marquee 8s linear infinite !important;
  animation-delay: 1s !important;
}

/* <PERSON>usar al hacer hover */
#dropdownMenuLink.text-overflow:hover .marquee-text {
  animation-play-state: paused !important;
}

/* Animación marquesina para texto largo mejorada */
@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  12.5% {
    transform: translateX(0%);
  }
  37.5% {
    transform: translateX(calc(-100% + 180px));
  }
  43.75% {
    transform: translateX(calc(-100% + 180px));
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(0%);
  }
}

/* Hacer más visible el botón dropdown del usuario */
#dropdownMenuLink::after {
  border-top: 0.4em solid !important;
  border-right: 0.3em solid transparent !important;
  border-left: 0.3em solid transparent !important;
  opacity: 0.8 !important;
  margin-left: 8px !important;
}

/* Efecto hover para el botón dropdown */
#dropdownMenuLink:hover::after {
  opacity: 1 !important;
  border-top-color: #ffffff !important;
}

/* Mejorar la visibilidad general del enlace */
#dropdownMenuLink:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

.lightLogoMerco{
  background-color: rgb(255, 255, 255) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 6px !important;
  padding: 2px 2px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.lightLogoZavi{
  background-color: rgb(255, 255, 255) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 6px !important;
  padding: 2px 2px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}



/* Sobrescribir Perfect Scrollbar con nuevo estándar - Mayor especificidad */
@media (forced-colors: active) {
  .ps-container,
  .ps-container.ps-active-x,
  .ps-container.ps-active-y {
    overflow: auto !important;
  }
}

/* Asegurar que funcione en todos los casos */
.ps-container {
  overflow: auto !important;
}


.user-profile .profile-img::before {
    -webkit-animation: 2.5s blow 0s linear infinite;
    animation: 2.5s blow 0s linear infinite;
    position: absolute;
    content: "";
    width: 50px;
    height: 50px;
    top: 35px;
    border-radius: 50%;
    z-index: 0;
}