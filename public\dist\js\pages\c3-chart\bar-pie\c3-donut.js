/*************************************************************************************/
// -->Template Name: Bootstrap Press Admin
// -->Author: Themedesigner
// -->Email: niravjoshi87 @gmail.com
// -->File: c3_chart_JS
/*************************************************************************************/
$(function () {
  var o = c3.generate({
    bindto: "#donut-chart",
    color: { pattern: ["#1e88e5", "#26c6da", "#f62d51"] },
    data: {
      columns: [
        ["option1", 30],
        ["option2", 120],
      ],
      type: "donut",
      onclick: function (o, n) {
        console.log("onclick", o, n);
      },
      onmouseover: function (o, n) {
        console.log("onmouseover", o, n);
      },
      onmouseout: function (o, n) {
        console.log("onmouseout", o, n);
      },
    },
    donut: { title: "Total Sale" },
  });
  setTimeout(function () {
    o.load({
      columns: [
        [
          "iphone",
          0.2,
          0.2,
          0.2,
          0.2,
          0.2,
          0.4,
          0.3,
          0.2,
          0.2,
          0.1,
          0.2,
          0.2,
          0.1,
          0.1,
          0.2,
          0.4,
          0.4,
          0.3,
          0.3,
          0.3,
          0.2,
          0.4,
          0.2,
          0.5,
          0.2,
          0.2,
          0.4,
          0.2,
          0.2,
          0.2,
          0.2,
          0.4,
          0.1,
          0.2,
          0.2,
          0.2,
          0.2,
          0.1,
          0.2,
          0.2,
          0.3,
          0.3,
          0.2,
          0.6,
          0.4,
          0.3,
          0.2,
          0.2,
          0.2,
          0.2,
        ],
        [
          "windows",
          1.4,
          1.5,
          1.5,
          1.3,
          1.5,
          1.3,
          1.6,
          1,
          1.3,
          1.4,
          1,
          1.5,
          1,
          1.4,
          1.3,
          1.4,
          1.5,
          1,
          1.5,
          1.1,
          1.8,
          1.3,
          1.5,
          1.2,
          1.3,
          1.4,
          1.4,
          1.7,
          1.5,
          1,
          1.1,
          1,
          1.2,
          1.6,
          1.5,
          1.6,
          1.5,
          1.3,
          1.3,
          1.3,
          1.2,
          1.4,
          1.2,
          1,
          1.3,
          1.2,
          1.3,
          1.3,
          1.1,
          1.3,
        ],
        [
          "android",
          2.5,
          1.9,
          2.1,
          1.8,
          2.2,
          2.1,
          1.7,
          1.8,
          1.8,
          2.5,
          2,
          1.9,
          2.1,
          2,
          2.4,
          2.3,
          1.8,
          2.2,
          2.3,
          1.5,
          2.3,
          2,
          2,
          1.8,
          2.1,
          1.8,
          1.8,
          1.8,
          2.1,
          1.6,
          1.9,
          2,
          2.2,
          1.5,
          1.4,
          2.3,
          2.4,
          1.8,
          1.8,
          2.1,
          2.4,
          2.3,
          1.9,
          2.3,
          2.5,
          2.3,
          1.9,
          2,
          2.3,
          1.8,
        ],
      ],
    });
  }, 1500),
    setTimeout(function () {
      o.unload({ ids: "option1" }), o.unload({ ids: "option2" });
    }, 2500);
});
