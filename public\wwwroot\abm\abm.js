class cfuncionesabm {
    //#region Configuración inicial para la carga de la grilla

    TABLA = "";
    SQL = "";
    CONSULTA = "";
    ESTADO = 1;
    columnas = [{ title: "Acción", data: "ACCION", visible: true }];
    CLAVE_PRIMARIA = "";
    FUNCION_ANTES_GUARDAR_EDITAR = "";
    FUNCION_DESPUES_GUARDAR = "";
    FUNCION_ABRIR_MODAL_GUARDAR = "";
    FUNCION_ABRIR_MODAL_EDIT = "";
    TABLAS_ASOCIADAS = [];

    async inicializarabm(dts_form) {
        try {
            const dts_grilla = await webService.callWebservice("PROC_GET_GRILLA_X_ID_GRILLA", { PID_GRILLA: dts_form.ID_GRILLA });
            if (Array.isArray(dts_grilla) && dts_grilla.length > 0) {
                this.TABLA = dts_form.TABLA;
                this.SQL = dts_grilla[0].CONSULTA;
                this.CLAVE_PRIMARIA = $("#ID_CLAVE_PRIMARIA")[0]?.name || "";
                this.CONSULTA = this.armarconsulta();

                dts_grilla.forEach(col => {
                    if (col.DATA) {
                        this.columnas.push({
                            title: col.TITLE,
                            data: col.DATA,
                            visible: col.VISIBLE
                        });
                    }
                });

                this.cargardatosgrillaabm();
            }
        } catch (error) {
            console.error("Error al inicializar ABM:", error);
        }
    }

    armarconsulta(tabla = this.TABLA, sql = this.SQL) {
        if (sql) {
            return `SELECT * FROM (${sql}) P WHERE P.ACTIVO = ${this.ESTADO}`;
        }
        return `SELECT * FROM ${tabla} P WHERE P.ACTIVO = ${this.ESTADO}`;
    }

    async cargardatosgrillaabm() {
        try {
            const datos = await webService.callWebservice(this.CONSULTA, {}, 1);
            const id = "#tbgrilla";

            if ($.fn.DataTable.isDataTable(id)) {
                $(id).DataTable().clear().destroy();
            }

            const data = (datos?.length > 0) ? this.configurarcolumnasabm(datos) : [];

            const opcionesDatatable = {
                data,
                columns: this.columnas,
                order: [[1, "asc"]],
                scrollX: true,
                dom: "Bfrtlip",
                buttons: [
                    "excel",
                    {
                        extend: "print",
                        text: "Imprimir"
                    },
                    {
                        className: "btn_activo_inactivo",
                        text: `
                        <i class="ti-na text text-danger" aria-hidden="true"></i>
                        <span class="text text-danger">Inactivos</span>
                        <i class="ti-check text-active" aria-hidden="true"></i>
                        <span class="text-active">Activos</span>
                    `,
                        action: () => {
                            this.ESTADO = $(".btn_activo_inactivo").attr("aria-pressed") === "true" ? 0 : 1;
                            this.CONSULTA = this.armarconsulta();
                            this.recargarDatosGrillaABM();
                        }
                    },
                    {
                        className: "btn_nuevo",
                        text: 'Nuevo Registro <span class="mdi mdi-plus-box"></span>',
                        action: () => {
                            this.nuevoregistro();
                        }
                    }
                ],
                language: {
                    lengthMenu: "Mostrar _MENU_ registros por página",
                    zeroRecords: "No existen coincidencias",
                    info: "Mostrando la página _PAGE_ de _PAGES_",
                    infoEmpty: "No hay registros disponibles",
                    search: "Buscar:",
                    paginate: {
                        first: "Primero",
                        last: "Último",
                        next: "Sigte.",
                        previous: "Anterior"
                    },
                }
            };

            $(id).DataTable(opcionesDatatable);

            setTimeout(() => {
                $(id).DataTable().columns.adjust();
            }, 50);

            $(".buttons-copy, .buttons-csv, .buttons-print, .buttons-pdf, .buttons-excel")
                .addClass("btn btn-info mr-1");
            $(".btn_nuevo").addClass("btn waves-effect waves-light btn-outline-info");
            $(".btn_activo_inactivo")
                .addClass("btn btn-light-info text-info font-weight-medium")
                .attr("data-bs-toggle", "button");

        } catch (error) {
            console.error("Error al cargar la grilla ABM:", error);
        }
    }

    configurarcolumnasabm(datos = []) {
        const campoFecha = [];
        const campoCheck = [];
        const campoNumerico = [];

        if (datos.length > 0) {
            Object.keys(datos[0]).forEach(columna => {
                const selector = `.${this.TABLA} [name=${columna}]`;
                const elemento = $(selector)[0];

                if (elemento) {
                    if (elemento.type === "date") campoFecha.push(columna);
                    if (elemento.type === "checkbox") campoCheck.push(columna);
                    if (elemento.className.includes("numero")) campoNumerico.push(columna);
                }
            });

            datos.forEach(fila => {
                campoFecha.forEach(c => {
                    fila[c] = fila[c] ? moment(fila[c], "YYYY-MM-DD").format("DD/MM/YYYY") : "";
                });

                campoCheck.forEach(c => {
                    fila[c] = fila[c] ? "Si" : "No";
                });

                campoNumerico.forEach(c => {
                    fila[c] = clickFunciones.formatearNumeroinicio(fila[c]);
                });

                const id = fila[this.CLAVE_PRIMARIA];
                fila.ACCION = `
                <button class="btn btn-info mr-1" onclick="Funcionabm.verabm(${id})" title="Ver">
                    <span class="mdi mdi-eye"></span>
                </button>
                <button class="btn btn-outline-warning" onclick="Funcionabm.editarabm(${id})" title="Editar">
                    <span class="mdi mdi-lead-pencil"></span>
                </button>
            `;

                if (fila.ACTIVO) {
                    fila.ESTADO = "Activo";
                    fila.ACCION += `
                    <button class="btn waves-effect waves-light btn-outline-danger" onclick="Funcionabm.eliminarabm(${id})" title="Borrar">
                        <span class="mdi mdi-delete"></span>
                    </button>
                `;
                } else {
                    fila.ESTADO = "Inactivo";
                    fila.ACCION += `
                    <button class="btn waves-effect waves-light btn-outline-danger" onclick="Funcionabm.eliminadopermanente(${id})" title="Borrar">
                        <span class="mdi mdi-delete"></span>
                    </button>
                `;
                }
            });
        }

        return datos;
    }

    async recargarDatosGrillaABM() {
        try {
            const datos = await webService.callWebservice(this.CONSULTA, {}, 1);
            const data = this.configurarcolumnasabm(datos);
            const tabla = $('#tbgrilla').DataTable();
            tabla.clear();
            tabla.rows.add(data);
            tabla.draw();
        } catch (error) {
            console.error("Error al recargar datos en la grilla ABM:", error);
        }
    }
    //#endregion

    //#region configuración del ABM configurado en el modal
    limpiarCamposABM(tabla = this.TABLA) {
        try {
            const controles = $(`.${tabla} [name]`);

            controles.each((index, control) => {
                const $control = $(control);
                const id = `#${control.name}`;

                $(id).hide();
                $control.removeClass("is-invalid").val("");

                if (control.type === "select-one") {
                    $(`#select2-${control.id}-container`).text("");
                }

                if (control.name === "ACTIVO") {
                    $control.val(1);
                }
            });

            if (tabla !== this.TABLA) {
                $(`#agregar_${tabla}`).show();
                $(`#editar_${tabla}`).hide().val("");
            } else {
                this.TABLAS_ASOCIADAS = [];
            }
        } catch (error) {
            console.error("Error al limpiar campos ABM:", error);
        }
    }

    validarDatosABM(tabla = this.TABLA) {
        try {
            let esValido = true;
            const controles = $(`.${tabla} [name]`);

            controles.each((index, control) => {
                if (!control.required) return;

                const $control = $(control);
                const id = control.name;
                const valor = $control.val();

                if (!valor) {
                    esValido = false;
                    $(`.${tabla} #${id}`).show();
                    $control.addClass("is-invalid");
                }

                if (control.type === "email" && valor && !this.validarEmail(valor)) {
                    esValido = false;
                    $(`.${tabla} #${id}`)
                        .html("El formato del correo es incorrecto")
                        .show();
                    $control.addClass("is-invalid");
                }
            });

            return esValido;
        } catch (error) {
            console.error("Error al validar datos ABM:", error);
            return false;
        }
    }

    validarEmail(email) {
        const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return regex.test(String(email).toLowerCase());
    }

    async duplicarabm(tabla = this.TABLA) {
        try {
            const controles = $(`.${tabla} [name]`);
            if (controles.length === 0) {
                console.log("No existen controles configurados");
                return;
            }
            const clavePrimaria = $(`.${tabla} [id=ID_CLAVE_PRIMARIA]`)[0];
            clavePrimaria.value = "";
            this.guardarabm(tabla);
        } catch (error) {
            console.error("Error al duplicar ABM:", error);
            return false;
        }
    }

    async guardarabm(tabla = this.TABLA) {
        try {
            const controles = $(`.${tabla} [name]`);
            if (controles.length === 0) {
                console.log("No existen controles configurados");
                return;
            }

            if (!this.validarDatosABM(tabla)) return;

            const clavePrimaria = $(`.${tabla} [id=ID_CLAVE_PRIMARIA]`)[0];

            // Validación antes de guardar/editar
            if (this.FUNCION_ANTES_GUARDAR_EDITAR) {
                const funcion = parent.window[this.FUNCION_ANTES_GUARDAR_EDITAR];
                const estado = funcion?.(tabla) ?? true;
                if (!estado) return;
            }

            // Preparar datos para guardar
            const { columnas, valores } = this.prepararDatosParaGuardar(controles, clavePrimaria);

            const objeto = {
                TABLA: tabla,
                ID_CLAVE_PRIMARIA: clavePrimaria.value || null,
                COLUMNAS: columnas.join(","),
                VALORES: valores.join(",")
            };

            // Llamar al servicio web
            const resultado = await webService.callWebservice("PROC_INSERTAR_REGISTRO", objeto);

            if (resultado?.[0]?.RESULTADO) {
                await this.procesarGuardadoExitoso(resultado[0], tabla, clavePrimaria);
            }
        } catch (error) {
            console.error("Error al guardar en ABM:", error);
            Swal.fire("Error", "No se pudo guardar el registro", "error");
        }
    }

    prepararDatosParaGuardar(controles, clavePrimaria) {
        const columnas = [];
        const valores = [];

        controles.each((index, control) => {
            columnas.push(control.name);

            if (control.value) {
                valores.push(this.formatearValor(control));
            } else {
                valores.push(this.obtenerValorPorDefecto(control, clavePrimaria));
            }
        });

        return { columnas, valores };
    }

    formatearValor(control) {
        if (isNaN(control.value)) {
            return control.classList.contains("numero")
                ? clickFunciones.formatearNumero(control.value)
                : `'${control.value}'`;
        }
        return control.value;
    }

    obtenerValorPorDefecto(control, clavePrimaria) {
        if (control.name === clavePrimaria.name) return "";
        if (control.type === "checkbox") return control.checked ? 1 : 0;
        return "NULL";
    }

    async procesarGuardadoExitoso(resultado, tabla, clavePrimaria) {
        // Guardar tablas asociadas si existen
        if (this.TABLAS_ASOCIADAS?.length > 0) {
            await this.guardarABMAsociadas(resultado.ID_GENERADOR, tabla);
        }

        // Ocultar modal
        $(`#modal_${tabla}`).modal("hide");

        // Ejecutar función después de guardar si existe
        if (this.FUNCION_DESPUES_GUARDAR) {
            const funcion = parent.window[this.FUNCION_DESPUES_GUARDAR];
            funcion?.(resultado.ID_GENERADOR, tabla);
        }

        // Mostrar feedback al usuario
        const accion = clavePrimaria.value ? "Modificado!" : "Guardado!";
        await Swal.fire(accion, "El proceso se realizó con éxito", "success");

        // Recargar o limpiar según el contexto
        variables_url.get("externo")
            ? this.limpiarCamposABM(tabla)
            : this.recargarDatosGrillaABM();
    }

    //#endregion

    //#region botones de las grillas
    nuevoregistro(tabla = this.TABLA) {
        try {
            this.limpiarCamposABM(tabla);

            if (this.FUNCION_ABRIR_MODAL_GUARDAR) {
                const funcion = parent.window[this.FUNCION_ABRIR_MODAL_GUARDAR];
                funcion?.(tabla);
            }

            $("#solovista").prop("disabled", false);
            $("#btnguardar").show();
            $("#btnduplicar").hide();
            $(`#modal_${tabla}`).modal("show");
        } catch (error) {
            console.error("Error en nuevoregistro:", error);
        }
    }

    async asignarvaloreacampos(ID_CLAVE_PRIMARIA, tabla = this.TABLA) {
        try {
            this.limpiarCamposABM(tabla);
            const clave_primaria = $(`.${tabla} [id=ID_CLAVE_PRIMARIA]`)[0];
            let datos = [];

            // Obtener datos según el tipo de tabla
            if (tabla === this.TABLA) {
                if (this.CONSULTA) {
                    const query = `${this.CONSULTA} AND ${clave_primaria.name} = ${ID_CLAVE_PRIMARIA}`;
                    datos = await webService.callWebservice(query, {}, 1);
                }
            } else {
                const dts_grilla = await webService.callWebservice("PROC_GET_GRILLA_X_ID", { PID: tabla });
                const consult_data = dts_grilla[0]?.CONSULTA || "";
                const consulta = this.armarconsulta(tabla, consult_data);
                const query = `${consulta} AND ${clave_primaria.name} = ${ID_CLAVE_PRIMARIA}`;
                datos = await webService.callWebservice(query, {}, 1);
            }

            // Asignar valores a los controles
            if (datos?.length > 0) {
                const registro = datos[0];
                const controles = $(`.${tabla} [name]`);

                controles.each((index, control) => {
                    const nombreCampo = control.name;
                    if (nombreCampo in registro) {
                        const valor = registro[nombreCampo];
                        this.asignarValorAControl(control, valor);
                    }
                });
            }

            // Llamar a función de edición si existe
            if (this.FUNCION_ABRIR_MODAL_EDIT) {
                const funcion = parent.window[this.FUNCION_ABRIR_MODAL_EDIT];
                funcion?.(ID_CLAVE_PRIMARIA, tabla);
            }
        } catch (error) {
            console.error("Error en asignarvaloreacampos:", error);
        }
    }

    asignarValorAControl(control, valor) {
        const $control = $(control);

        if (!String(valor)) return;

        switch (control.type) {
            case "date":
                $control.val(moment(valor).format("YYYY-MM-DD"));
                break;
            case "checkbox":
                $control.prop("checked", valor);
                break;
            default:
                $control.val(valor);
        }
    }

    editarabm(ID_CLAVE_PRIMARIA, tabla = this.TABLA) {
        try {
            const controles = $(`.${tabla} [name]`);
            if (controles.length > 0) {
                $(`#modal_${tabla}`).modal("show");
                $("#solovista").prop("disabled", false);
                $("#btnguardar").show();
                $("#btnduplicar").show();
                this.asignarvaloreacampos(ID_CLAVE_PRIMARIA, tabla);
            }
        } catch (error) {
            console.error("Error en editarabm:", error);
        }
    }

    verabm(ID_CLAVE_PRIMARIA, tabla = this.TABLA) {
        try {
            const controles = $(`.${this.TABLA} [name]`);
            if (controles.length > 0) {
                $("#solovista").prop("disabled", true);
                $("#btnguardar").hide();
                $("#btnduplicar").hide();
                this.asignarvaloreacampos(ID_CLAVE_PRIMARIA, tabla);
                $(`#modal_${tabla}`).modal("show");
            }
        } catch (error) {
            console.error("Error en verabm:", error);
        }
    }

    eliminarabm(ID_CLAVE_PRIMARIA, tabla = this.TABLA) {
        try {
            Swal.fire({
                title: '¿Desea eliminar el registro seleccionado?',
                icon: 'warning',
                width: '580px',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                cancelButtonText: 'No',
                confirmButtonText: 'Sí'
            }).then((result) => {
                if (result.isConfirmed) {
                    this.eliminadologico(ID_CLAVE_PRIMARIA, tabla);
                }
            });
        } catch (error) {
            console.error("Error en eliminarabm:", error);
        }
    }

    async eliminadologico(ID_CLAVE_PRIMARIA, tabla = this.TABLA) {
        try {
            const clave_primaria = $(`.${tabla} [id=ID_CLAVE_PRIMARIA]`)[0];

            const objeto = {
                TABLA: this.TABLA,
                ID_CLAVE_PRIMARIA: ID_CLAVE_PRIMARIA,
                COLUMNAS: [clave_primaria.name, "ACTIVO"].join(","),
                VALORES: [ID_CLAVE_PRIMARIA, 0].join(",")
            };

            const resultado = await webService.callWebservice("PROC_INSERTAR_REGISTRO", objeto);

            if (resultado?.[0]?.RESULTADO) {
                await Swal.fire("Eliminado", "El proceso se realizó con éxito", "success");
                this.recargarDatosGrillaABM();
            }
        } catch (error) {
            console.error("Error en eliminadologico:", error);
        }
    }

    async eliminadopermanente(ID_CLAVE_PRIMARIA, tabla = this.TABLA) {
        try {
            const result = await Swal.fire({
                title: '¿Desea eliminar el registro de forma permanente?',
                icon: "warning",
                width: '580px',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                cancelButtonText: 'No',
                confirmButtonText: 'Sí'
            });

            if (result.isConfirmed) {
                const clave_primaria = $(`.${tabla} [id=ID_CLAVE_PRIMARIA]`)[0];

                await webService.callWebservice("PROC_ELIMINAR_REGISTRO", {
                    NOMBRE_TABLA: tabla,
                    CLAVE_PRIMARIA: clave_primaria.name,
                    VALOR_CLAVE_PRIMARIA: ID_CLAVE_PRIMARIA
                });

                await Swal.fire("Eliminado", "El proceso se realizó con éxito", "success");
                this.recargarDatosGrillaABM();
            }
        } catch (error) {
            console.error("Error en eliminadopermanente:", error);
        }
    }
    //#endregion
    //#region funciones que corresponden a las tablas asociadas
    async iniciagrillatablaasociada(id, tabla = this.TABLA) {
        try {
            if (!id) return;

            const dts_grilla = await clickFunciones.callWebservice("PROC_GET_GRILLA_X_ID", { PID: id });
            const idtabla = '#' + id;

            // Limpiar DataTable si existe
            if ($.fn.DataTable.isDataTable(idtabla)) {
                $(idtabla).DataTable().destroy();
            }

            let consulta = "";
            if (dts_grilla?.length > 0) {
                const control_id = $(`.${tabla} [id=ID_CLAVE_PRIMARIA]`);
                if (control_id.length && control_id.val()) {
                    consulta = `${dts_grilla[0].CONSULTA}${control_id.val()}`;
                }

                this.addtablaasociada(dts_grilla[0].TABLA_ASOCIADA, tabla);
            }

            // Obtener datos y configurar DataTable
            const datos = consulta ? await webService.callWebservice(consulta, {}, 1) : [];
            const data = this.configurarcolumnasabmasociado(datos, dts_grilla[0]?.TABLA_ASOCIADA, dts_grilla[0]?.ES_EDITABLE);
            const columnas = this.prepararcolumnas(dts_grilla);

            // Configurar botones
            const botones = [
                {
                    className: 'btn btn-outline-secondary',
                    text: `Cancelar <i class="mdi mdi-eraser"></i>`,
                    attr: { id: `limpiar_${tabla}` },
                    action: () => this.limpiarCamposABM(dts_grilla[0].TABLA_ASOCIADA)
                },
                {
                    className: 'btn btn-outline-info',
                    text: 'Agregar <i class="mdi mdi-plus"></i>',
                    attr: { id: `agregar_${dts_grilla[0].TABLA_ASOCIADA}` },
                    action: () => this.agregarnuevafilaabmaso(dts_grilla[0].TABLA_ASOCIADA)
                }
            ];

            if (dts_grilla[0]?.ES_EDITABLE) {
                botones.push({
                    className: 'btn btn-outline-warning hide',
                    text: 'Modificar <i class="mdi mdi-pencil"></i>',
                    attr: { id: `editar_${dts_grilla[0].TABLA_ASOCIADA}` },
                    action: () => this.modificarfilaabmaso(dts_grilla[0].TABLA_ASOCIADA)
                });
            }

            // Configuración del DataTable
            const datatableOptions = {
                data: data,
                columns: columnas,
                order: [[1, "asc"], [0, "asc"]],
                scrollX: true,
                dom: "Bfrtlip",
                buttons: botones,
                language: {
                    lengthMenu: "Mostrar _MENU_ registros por página",
                    zeroRecords: "No existen coincidencias",
                    info: "Mostrando la página _PAGE_ de _PAGES_",
                    infoEmpty: "No hay registros disponibles",
                    search: "Buscar:",
                    paginate: {
                        first: "Primero",
                        last: "Último",
                        next: "Siguiente",
                        previous: "Anterior"
                    }
                }
            };

            const dt = $(idtabla).DataTable(datatableOptions);
            setTimeout(() => dt.columns.adjust(), 50);
            $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
                const targetPaneSelector = $(e.target).attr('href');
                $(`${targetPaneSelector} table.dataTable`).each(function () {
                    const dts = $(this).DataTable();
                    dts.columns.adjust();
                });
                setTimeout(() => {

                }, 5);
            });

        } catch (error) {
            console.error("Error en iniciagrillatablaasociada:", error);
        }
    }

    addtablaasociada(TABLA_ASOCIADA, tabla = this.TABLA) {
        try {
            if (!this.TABLAS_ASOCIADAS.includes(tabla)) {
                this.TABLAS_ASOCIADAS.push(tabla);
            }

            if (TABLA_ASOCIADA) {
                const tablas = this.TABLAS_ASOCIADAS[tabla] || [];
                tablas.push(TABLA_ASOCIADA);
                this.TABLAS_ASOCIADAS[tabla] = tablas;
            }
        } catch (error) {
            console.error("Error en addtablaasociada:", error);
        }
    }

    prepararcolumnas(dts_grilla) {
        try {
            const columnas = [{
                title: "Acción",
                data: "ACCION",
                visible: true,
                clave_referencia: 0,
                referencia_columna: 0
            }];

            dts_grilla?.forEach(col => {
                if (col.DATA) {
                    columnas.push({
                        title: col.TITLE,
                        data: col.DATA,
                        visible: col.VISIBLE,
                        clave_referencia: col.CLAVE_REFERENCIA,
                        referencia_columna: col.REFERENCIA_COLUMNA
                    });
                }
            });

            return columnas;
        } catch (error) {
            console.error("Error en prepararcolumnas:", error);
            return [];
        }
    }

    configurarcolumnasabmasociado(datos = [], tabla_asociada, es_editable = 0) {
        try {
            if (!datos?.length) return datos;

            const campocheck = [];
            const campoNum = [];

            // Identificar tipos de campos
            Object.keys(datos[0]).forEach(column => {
                const control = $(`.${tabla_asociada} [name=${column}]`)[0];
                if (control) {
                    if (control.type === "checkbox") campocheck.push(column);
                    if (control.className.includes("numero")) campoNum.push(column);
                }
            });

            // Formatear datos
            return datos.map(item => {
                campocheck.forEach(col => item[col] = item[col] ? "Si" : "No");
                campoNum.forEach(col => item[col] = clickFunciones.formatearNumeroinicio(item[col]));

                item.ACCION = `
                <button class="btn waves-effect waves-light btn-outline-danger" 
                    onclick="Funcionabm.borrarabmasociado(this, '${tabla_asociada}'); return false;" 
                    tabindex="0" aria-controls="tbgrilla" title="Borrar">
                    <span class="mdi mdi-delete"></span>
                </button>
            `;

                if (es_editable) {
                    item.ACCION += `
                    <button class="btn waves-effect waves-light btn-outline-warning" 
                        onclick="Funcionabm.editarabmasociado(this, '${tabla_asociada}'); return false;" 
                        tabindex="0" aria-controls="tbgrilla" title="Editar">
                        <span class="mdi mdi-lead-pencil"></span>
                    </button>
                    
            <button class="btn waves-effect waves-light btn-outline-warning" 
                onclick="Funcionabm.editarabmasociado(this, '${tabla_asociada}'); return false;" 
                tabindex="0" aria-controls="tbgrilla" title="Editar">
                <span class="mdi mdi-lead-pencil"></span>
            </button>
                `;
                }

                return item;
            });
        } catch (error) {
            console.error("Error en configurarcolumnasabmasociado:", error);
            return datos;
        }
    }

    agregarnuevafilaabmaso(tabla_asociada) {
        try {
            const controles = $(`.${tabla_asociada} [name]`);
            if (!controles.length) {
                console.log("No existen controles configurados");
                return;
            }

            if (!this.validarDatosABM(tabla_asociada)) return;

            const dt = $(`#${tabla_asociada}`).DataTable();
            const columnas = dt.settings().init().columns.map(col => col.data);
            const datosActuales = Funciongrilla.getdatosgrilla(tabla_asociada);
            const nuevoRegistro = {};

            columnas.forEach(col => nuevoRegistro[col] = "");

            // Configurar acciones
            nuevoRegistro.ACCION = `
            <button class="btn waves-effect waves-light btn-outline-danger" 
                onclick="Funcionabm.borrarabmasociado(this, '${tabla_asociada}'); return false;" 
                tabindex="0" aria-controls="tbgrilla" title="Borrar">
                <span class="mdi mdi-delete"></span>
            </button>
            <button class="btn waves-effect waves-light btn-outline-warning" 
                onclick="Funcionabm.editarabmasociado(this, '${tabla_asociada}'); return false;" 
                tabindex="0" aria-controls="tbgrilla" title="Editar">
                <span class="mdi mdi-lead-pencil"></span>
            </button>
        `;

            // Asignar valores de los controles
            controles.each((index, control) => {
                if (!columnas.includes(control.name)) return;

                if ($(control).is("select")) {
                    nuevoRegistro[control.name] = control.value;
                    nuevoRegistro[control.id] = $(control.selectedOptions).text();
                } else if ($(control).is("input")) {
                    nuevoRegistro[control.name] = control.type === "checkbox"
                        ? control.checked ? "Si" : "No"
                        : control.value;
                }
            });

            if (!this.validaunicidadreferencia(datosActuales, nuevoRegistro, tabla_asociada)) return;

            Funciongrilla.agregardatosgrilla(tabla_asociada, [...datosActuales, nuevoRegistro]);
            this.limpiarCamposABM(tabla_asociada);
        } catch (error) {
            console.error("Error en agregarnuevafilaabmaso:", error);
        }
    }

    async borrarabmasociado(this_btn, tabla_asociada) {
        try {
            const fila = Funciongrilla.getfilabtn(tabla_asociada, this_btn);
            const datos = Funciongrilla.getdatosgrilla(tabla_asociada, fila)[0];

            const result = await Swal.fire({
                title: '¿Desea eliminar el registro de forma permanente?',
                icon: "warning",
                width: '580px',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                cancelButtonText: 'No',
                confirmButtonText: 'Sí'
            });

            if (result.isConfirmed) {
                const claves_primarias = this.getclavesprimariasgrillaabmaso(tabla_asociada);
                const valores = claves_primarias.map(clave => datos[clave]);

                await clickFunciones.callWebservice("PROC_ELIMINAR_REGISTRO", {
                    NOMBRE_TABLA: tabla_asociada,
                    CLAVE_PRIMARIA: claves_primarias.join(";"),
                    VALOR_CLAVE_PRIMARIA: valores.join(";")
                });

                Funciongrilla.eliminarfila(tabla_asociada, fila);
                await Swal.fire("Eliminado", "El proceso se realizó con éxito", "success");
            }
        } catch (error) {
            console.error("Error en borrarabmasociado:", error);
        }
    }

    editarabmasociado(this_btn, tabla_asociada) {
        try {
            const fila = Funciongrilla.getfilabtn(tabla_asociada, this_btn);
            const controles = $(`.${tabla_asociada} [name]`);
            if (!controles.length) {
                console.log("No existen controles configurados");
                return;
            }

            const datos = Funciongrilla.getdatosgrilla(tabla_asociada, fila)[0];
            const columnas = $(`#${tabla_asociada}`).DataTable().settings().init().columns.map(col => col.data);

            controles.each((index, control) => {
                if (!columnas.includes(control.name)) return;

                if ($(control).is("select")) {
                    $(control).val(datos[control.name]).trigger('change');
                } else if ($(control).is("input")) {
                    if (control.type === "checkbox") {
                        control.checked = datos[control.name] === "Si";
                    } else {
                        control.value = datos[control.name] || "";
                    }
                }
            });

            $(`#agregar_${tabla_asociada}`).hide();
            $(`#editar_${tabla_asociada}`).show().val(fila);
        } catch (error) {
            console.error("Error en editarabmasociado:", error);
        }
    }

    modificarfilaabmaso(tabla_asociada) {
        try {
            const controles = $(`.${tabla_asociada} [name]`);
            if (!controles.length) {
                console.log("No existen controles configurados");
                return;
            }

            if (!this.validarDatosABM(tabla_asociada)) return;

            const fila = $(`#editar_${tabla_asociada}`).val();
            const datos = Funciongrilla.getdatosgrilla(tabla_asociada);
            const columnas = $(`#${tabla_asociada}`).DataTable().settings().init().columns.map(col => col.data);

            controles.each((index, control) => {
                if (!columnas.includes(control.name)) return;

                if ($(control).is("select")) {
                    datos[fila][control.name] = control.value;
                    datos[fila][control.id] = $(control.selectedOptions).text();
                } else if ($(control).is("input")) {
                    datos[fila][control.name] = control.type === "checkbox"
                        ? control.checked ? "Si" : "No"
                        : control.value;
                }
            });

            Funciongrilla.editarfilagrilla(tabla_asociada, fila, datos);
            this.limpiarCamposABM(tabla_asociada);
            $(`#agregar_${tabla_asociada}`).show();
            $(`#editar_${tabla_asociada}`).hide().val("");
        } catch (error) {
            console.error("Error en modificarfilaabmaso:", error);
        }
    }

    getclavesprimariasgrillaabmaso(id) {
        try {
            const dt = $(`#${id}`).DataTable();
            return dt.settings().init().columns
                .filter(col => col.clave_referencia)
                .map(col => col.data);
        } catch (error) {
            console.error("Error en getclavesprimariasgrillaabmaso:", error);
            return [];
        }
    }

    async guardarABMAsociadas(ID_CLAVE, tabla = this.TABLA) {
        try {
            const clave_primaria = $(`.${tabla} [id=ID_CLAVE_PRIMARIA]`)[0];
            const tablasAsociadas = this.TABLAS_ASOCIADAS[tabla] || [];

            for (const tabla_asociada of tablasAsociadas) {
                const datos = Funciongrilla.getdatosgrilla(tabla_asociada);
                if (!datos?.length) continue;

                const dt = $(`#${tabla_asociada}`).DataTable();
                const columnasReferencia = dt.settings().init().columns
                    .filter(col => col.referencia_columna)
                    .map(col => col.data);

                for (const fila of datos) {
                    const valores = columnasReferencia.map(col => {
                        if (col === clave_primaria.name) return ID_CLAVE;

                        const control = $(`.${tabla_asociada} [name=${col}]`)[0];
                        //if (!control) return "NULL";

                        let valor = fila[col];
                        if (control) {
                            if (control.type === "checkbox") {
                                valor = valor === "Si" ? 1 : 0;
                            } else if (control.className.includes("numero")) {
                                valor = clickFunciones.formatearNumero(valor);
                            } else if (typeof valor === "string") {
                                valor = `'${valor.replace(/'/g, "''")}'`; // Escapar comillas simples dentro del string
                            }
                        }

                        if (valor == null || valor === '') return "NULL";

                        return valor;
                    });

                    const objeto = {
                        TABLA: tabla_asociada,
                        ID_CLAVE_PRIMARIA: ID_CLAVE || null,
                        COLUMNAS: columnasReferencia.join(","),
                        VALORES: valores.join(",")
                    };

                    await webService.callWebservice("PROC_INSERTAR_REGISTRO", objeto);
                }
            }
        } catch (error) {
            console.error("Error en guardarABMAsociadas:", error);
        }
    }

    validaunicidadreferencia(dts_grid, dts_new, tabla_asociada = this.TABLA) {
        try {
            const clavePrimariaPrincipal = $(`.${this.TABLA} [id=ID_CLAVE_PRIMARIA]`)[0];
            const clavePrimaria = this.getclavesprimariasgrillaabmaso(tabla_asociada);

            const dts_add = clavePrimaria
                .filter(pk => dts_new[pk] && dts_new[pk] !== clavePrimariaPrincipal?.name)
                .map(pk => dts_new[pk]);

            const pk_list = dts_grid.map(data =>
                clavePrimaria
                    .filter(pk => dts_new[pk] && dts_new[pk] !== clavePrimariaPrincipal?.name)
                    .map(pk => data[pk])
                    .join(",")
            );

            if (pk_list.includes(dts_add.join(","))) {
                Swal.fire("Atención", "El registro ya se encuentra dentro del registro", "info");
                return false;
            }
            return true;
        } catch (error) {
            console.error("Error en validaunicidadreferencia:", error);
            return false;
        }
    }

    async generararchivo(nombreRuta, datos = {}) {
        try {
            // Aseguramos que datos sea un objeto y tenga nombre_tabla
            if (!datos || typeof datos !== 'object') {
                datos = { nombre_tabla: '' };
            }
            let ruta = "creararchivo"
            if (datos.nombre_tabla)
                ruta = "creararchivoabm"
            const response = await fetch(`${BASE_URL}/${ruta}/${encodeURIComponent(nombreRuta)}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(datos)
            });

            if (!response.ok) throw new Error(`Error HTTP: ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error('Error en generararchivo:', error);
            throw error;
        }
    }
    //#endregion
}

var Funcionabm = new cfuncionesabm();