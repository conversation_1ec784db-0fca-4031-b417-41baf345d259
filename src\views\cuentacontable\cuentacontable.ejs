<%- include('../abm/abm.ejs') %>

    <div class="modal fade" id="modal_CUENTAS_CONTABLES" data-bs-backdrop="static" data-bs-keyboard="false"
        tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-info text-white">
                    <h4 class="modal-title" id="info-header-modalLabel">
                        Cuentas Contables
                    </h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="card">
                        <form>
                            <fieldset id="solovista">
                                <div class="row CUENTAS_CONTABLES">

                                    <div class="col-md-4" style="display: none;">
                                        <div class="form-floating mb-3">
                                            <input id="ID_CLAVE_PRIMARIA" name="ID_CUENTA_CONTABLE" type="text"
                                                class="form-control" placeholder="Enter Name here">
                                            <label for="ID_CUENTA_CONTABLE">ID_CUENTA_CONTABLE</label>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div for="param_CODIGO" class="form-floating mb-2">
                                            <input type="text" class="form-control" id="param_CODIGO" name="CODIGO"
                                                placeholder="Ingrese codigo...">
                                            <label for="CODIGO">
                                                Codigo
                                            </label>
                                            <sd-feemall id="CODIGO" class="invalid-feedback">Campo Obligatorío</small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div for="param_NOMBRE" class="form-floating mb-2">

                                            <input type="text" class="form-control" id="param_NOMBRE" name="NOMBRE"
                                                placeholder="Ingrese nombre...">
                                            <label for="NOMBRE">
                                                Nombre
                                            </label>
                                            <sd-feemall id="NOMBRE" class="invalid-feedback">Campo Obligatorío</small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div for="param_ID_TIPO_CUENTA" class="form-floating mb-2">
                                            <!-- 
                                            <input type="number" class="form-control numero" id="param_ID_TIPO_CUENTA"
                                                name="ID_TIPO_CUENTA"> -->
                                            <select id="param_ID_TIPO_CUENTA" name="ID_TIPO_CUENTA" class="form-control"
                                                required tabindex="-1"> </select>
                                            <label for="ID_TIPO_CUENTA">
                                                Id Tipo Cuenta
                                            </label>
                                            <sd-feemall id="ID_TIPO_CUENTA" class="invalid-feedback">Campo
                                                Obligatorío</small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div for="param_ID_CUENTA_PADRE" class="form-floating mb-2">

                                            <!-- <input type="number" class="form-control numero" id="param_ID_CUENTA_PADRE"
                                                name="ID_CUENTA_PADRE"> -->
                                            <select id="param_ID_CUENTA_PADRE" name="ID_CUENTA_PADRE"
                                                class="form-control" required tabindex="-1"> </select>
                                            <label for="ID_CUENTA_PADRE">
                                                Id Cuenta Padre
                                            </label>
                                            <sd-feemall id="ID_CUENTA_PADRE" class="invalid-feedback">Campo
                                                Obligatorío</small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div for="param_NIVEL" class="form-floating mb-2">

                                            <select class="form-control" id="param_NIVEL" name="NIVEL">
                                                <option value="1">1</option>
                                                <option value="2">2</option>
                                                <option value="3">3</option>
                                                <option value="4">4</option>
                                                <option value="5">5</option>
                                                <option value="6">6</option>
                                                <option value="7">7</option>
                                                <option value="8">8</option>
                                                <option value="9">9</option>

                                            </select>
                                            <label for="NIVEL">
                                                Nivel
                                            </label>
                                            <sd-feemall id="NIVEL" class="invalid-feedback">Campo Obligatorío</small>
                                        </div>
                                    </div>


                                    <div class="col-md-4">
                                        <div for="param_ES_MOVIMIENTO" class="form-floating mb-2">

                                            <select class="form-control" id="param_ES_MOVIMIENTO" name="ES_MOVIMIENTO">
                                                <option value="1">Sí</option>
                                                <option value="0" selected>No</option>
                                            </select>
                                            <label for="ES_MOVIMIENTO">
                                                Es Movimiento
                                            </label>
                                            <sd-feemall id="ES_MOVIMIENTO" class="invalid-feedback">Campo
                                                Obligatorío</small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div for="param_ACTIVO" class="form-floating mb-2">

                                            <select class="form-control" id="param_ACTIVO" name="ACTIVO">
                                                <option value="1" selected>Activo</option>
                                                <option value="0">Inactivo</option>
                                            </select>
                                            <label for="ACTIVO">
                                                Estado
                                            </label>
                                            <sd-feemall id="ACTIVO" class="invalid-feedback">Campo Obligatorío</small>
                                        </div>
                                    </div>

                                </div>
                            </fieldset>
                        </form>
                        <%- include('../abm/modal-footer.ejs') %>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="../../wwwroot/cuentacontable/cuentacontable.js"></script>