<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 id="nomform" class="text-themecolor mb-0"></h3>
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/home">Home</a>
            </li>
            <li id="nomformbread" class="breadcrumb-item active"></li>
        </ol>
    </div>
</div>

<!-- Loading State -->
<div id="loading-state" class="miperfil-state d-flex justify-content-center align-items-center" style="min-height: 300px;">
    <div class="text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Cargando...</span>
        </div>
        <p class="mt-2">Cargando datos personales...</p>
    </div>
</div>

<!-- Error State -->
<div id="error-state" class="miperfil-state d-flex justify-content-center align-items-center" style="min-height: 300px;">
    <div class="text-center">
        <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
        <p class="mt-2">No se encontraron datos personales.</p>
        <button class="btn btn-primary mt-2" onclick="window.location.reload()">Reintentar</button>
    </div>
</div>

<!-- Inactive Account State -->
<div id="inactive-state" class="miperfil-state d-flex justify-content-center align-items-center" style="min-height: 300px;">
    <div class="text-center">
        <i class="fas fa-user-slash fa-3x text-danger mb-3"></i>
        <h4 class="text-danger mb-3">Cuenta Inactiva</h4>
        <p class="text-muted mb-4">Tu Cuenta Corriente no se encuentra activa.<br>Por favor, contacta a R.R.H.H. para más información.</p>
        <div class="d-flex gap-2 justify-content-center">
            <button id="verify-status-btn" class="btn btn-outline-primary" onclick="verificarEstadoCuenta()">
                <i class="fas fa-sync-alt me-2"></i>Verificar Estado
            </button>
        </div>
    </div>
</div>

<!-- Main Content -->
<div id="main-content" class="miperfil-state container-fluid py-3">
    <div class="row g-2">

        <!-- Left Column - Personal Information -->
        <div class="col-md-10">
            <div class="row g-3 h-100">

                <!-- Personal Information Section -->
                <div class="col-12">
                    <div class="card border-0 shadow h-100">
                        <div class="card-body p-4">
                            <div class="row g-3">

                                <!-- Name - Full Row -->
                                <div class="col-12">
                                    <div class="py-3 px-3 bg-light rounded-3 d-flex align-items-center">
                                        <i class="fas fa-user text-primary me-3"></i>
                                        <div class="flex-grow-1">
                                            <div class="text-muted small">Nombre Completo</div>
                                            <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center">
                                                <span id="nombres" class="fw-bold me-sm-2 mb-1 mb-sm-0">Sin información</span>
                                                <span id="estado-activo-badge" class="badge bg-success estado-activo-mobile" style="display: none;">
                                                    <i class="fas fa-check-circle me-1"></i>Activo
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Document | Gender - Side by Side -->
                                <div class="col-sm-6">
                                    <div class="py-3 px-3 bg-light rounded-3 d-flex align-items-center h-100">
                                        <i class="fas fa-id-card text-primary me-3"></i>
                                        <div>
                                            <div class="text-muted small">Documento</div>
                                            <div id="nroDocumento" class="fw-bold">Sin información</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-6">
                                    <div class="py-3 px-3 bg-light rounded-3 d-flex align-items-center h-100">
                                        <i class="fas fa-calendar-check text-primary me-3"></i>
                                        <div>
                                            <div class="text-muted small">Fecha de Ingreso</div>
                                            <div class="d-flex align-items-center">
                                                <span id="fechaIngreso" class="fw-bold">Sin información</span>
                                                <span id="antiguedad-badge" class="badge bg-dark ms-2" style="display: none;"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Birth Date | Credit Limit - Side by Side -->
                                <div class="col-sm-6">
                                    <div class="py-3 px-3 bg-light rounded-3 d-flex align-items-center h-100">
                                        <i class="fas fa-birthday-cake text-primary me-3"></i>
                                        <div>
                                            <div class="text-muted small">Fecha de Nacimiento</div>
                                            <div class="d-flex align-items-center">
                                                <span id="fechaNacimiento" class="fw-bold me-2">Sin información</span>
                                                <span id="edad-badge" class="badge bg-dark" style="display: none;"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-6">
                                    <div class="py-3 px-3 bg-light rounded-3 d-flex align-items-center h-100">
                                        <i class="fas fa-credit-card text-primary me-3"></i>
                                        <div>
                                            <div class="text-muted small">Límite de Crédito</div>
                                            <div id="limiteCredito" class="fw-bold">Sin información</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Email | Address - Side by Side -->
                                <div class="col-sm-6">
                                    <div class="py-3 px-3 bg-light rounded-3 d-flex align-items-center h-100">
                                        <i class="fas fa-envelope text-primary me-3"></i>
                                        <div>
                                            <div class="text-muted small">Correo Electrónico</div>
                                            <div id="correo" class="fw-bold">Sin información</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-6">
                                    <div class="py-3 px-3 bg-light rounded-3 d-flex align-items-center h-100">
                                        <i class="fas fa-home text-primary me-3"></i>
                                        <div>
                                            <div class="text-muted small">Domicilio</div>
                                            <div id="domicilio" class="fw-bold">Sin información</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gender | Location - Side by Side -->
                                <div class="col-sm-6">
                                    <div class="py-3 px-3 bg-light rounded-3 d-flex align-items-center h-100">
                                        <i class="fas fa-user text-primary me-3"></i>
                                        <div>
                                            <div class="text-muted small">Género</div>
                                            <div id="genero" class="fw-bold">Sin información</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-6">
                                    <div class="py-3 px-3 bg-light rounded-3 d-flex align-items-center h-100">
                                        <i class="fas fa-map-marker-alt text-primary me-3"></i>
                                        <div>
                                            <div class="text-muted small">Localidad</div>
                                            <div id="localidad" class="fw-bold">Sin información</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Fields (if needed) -->
                                <div class="col-sm-6" id="telefono-container" style="display: none;">
                                    <div class="py-3 px-3 bg-light rounded-3 d-flex align-items-center h-100">
                                        <i class="fas fa-phone text-primary me-3"></i>
                                        <div>
                                            <div class="text-muted small">Teléfono</div>
                                            <div id="telefono" class="fw-bold">Sin información</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-6" id="legajo-container" style="display: none;">
                                    <div class="py-3 px-3 bg-light rounded-3 d-flex align-items-center h-100">
                                        <i class="fas fa-id-badge text-primary me-3"></i>
                                        <div>
                                            <div class="text-muted small">Legajo</div>
                                            <div id="codlegajo" class="fw-bold">Sin información</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Go to Movements Button -->
                                <div class="col-12 d-flex justify-content-center">
                                    <div class="col-md-8">
                                        <div class="card border-0 shadow">
                                            <div class="card-body p-3">
                                                <div id="movements-button" class="d-flex align-items-center justify-content-between py-2 px-3 bg-light rounded-3" style="cursor: pointer; transition: background-color 0.2s;">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-exchange-alt text-dark me-3 fa-lg"></i>
                                                        <div>
                                                            <div class="fw-bold small text-dark">Ver movimientos</div>
                                                            <div class="text-muted fw-bold small">Consulta tu estado de cuenta</div>
                                                        </div>
                                                    </div>
                                                    <i class="fas fa-chevron-right text-muted"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - VirtualCard Placeholder -->

        <!-- <div class="col-md-5">
            <div class="row g-3">
                <div class="col-12">
                    <div class="card border-0 shadow">
                        <div class="card-body p-3">
                  
                            <div class="virtual-card-placeholder bg-gradient text-white rounded-3 p-4 text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 200px;">
                                <div class="d-flex flex-column justify-content-center h-100">
                                    <i class="fas fa-credit-card fa-3x mb-3"></i>
                                    <h5 class="mb-2">Tarjeta Virtual</h5>
                                    <p class="mb-0 small">Información de la tarjeta virtual</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->

    </div>
</div>


<style>
/* Custom styles for miperfil */
.miperfil-movements-button:hover {
    background-color: #e9ecef !important;
    cursor: pointer;
}

.virtual-card-placeholder {
    transition: transform 0.2s ease-in-out;
}

.virtual-card-placeholder:hover {
    transform: translateY(-2px);
}

.info-box {
    transition: box-shadow 0.2s ease-in-out;
}

.info-box:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Loading animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading-pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

/* State management - usando IDs para mayor especificidad */
#loading-state:not(.active),
#error-state:not(.active),
#inactive-state:not(.active),
#main-content:not(.active) {
    display: none !important;
}

#loading-state.active,
#error-state.active,
#inactive-state.active {
    display: flex !important;
}

#main-content.active {
    display: block !important;
}

/* Estado inicial - loading visible por defecto */
#loading-state {
    display: flex !important;
}

/* Forzar ocultamiento cuando no tienen clase active */
#loading-state.miperfil-state:not(.active) {
    display: none !important;
}

#error-state.miperfil-state:not(.active) {
    display: none !important;
}

#inactive-state.miperfil-state:not(.active) {
    display: none !important;
}

#main-content.miperfil-state:not(.active) {
    display: none !important;
}

/* Estilos para el badge de estado activo en móviles */
.estado-activo-mobile {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    white-space: nowrap;
}

/* Responsive adjustments para móviles */
@media (max-width: 576px) {
    .estado-activo-mobile {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        margin-top: 0.25rem;
        align-self: flex-start;
    }

    /* Ajustar el contenedor del nombre para mejor distribución */
    .py-3.px-3.bg-light.rounded-3 {
        padding: 0.75rem !important;
    }

    /* Reducir el margen del ícono en móviles */
    .fas.fa-user.text-primary.me-3 {
        margin-right: 0.75rem !important;
        font-size: 1rem;
    }
}
</style>

<script src="../../wwwroot/miperfil/miperfil.js"></script>