<%- include('../abm/abm.ejs') %>

    <div class="modal fade" id="modal_FORMULARIOS" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-info text-white">
                    <h4 class="modal-title" id="info-header-modalLabel">
                        Formulario
                    </h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="card FORMULARIOS">
                        <form>
                            <fieldset id="solovista">
                                <div class="row">
                                    <div class="col-md-6" style="display: none;">
                                        <div class="form-floating mb-3">
                                            <input id="ID_CLAVE_PRIMARIA" name="ID_FORMULARIO" type="text" class="form-control"
                                                placeholder="Enter Name here">
                                            <label for="ID_FORMULARIO">ID_FORMULARIO</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input name="NOMBRE" type="text" class="form-control" placeholder="Enter Name here" required>
                                            <label for="NOMBRE">Nombre</label>
                                            <sd-feemall id="NOMBRE" class="invalid-feedback">Favor agregar el
                                                Nombre</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input name="ROUTER" type="text" class="form-control" placeholder="Enter Name here" required>
                                            <label for="ROUTER">Url</label>
                                            <small id="ROUTER" class="invalid-feedback">Favor agregar la Url</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select id="ddl_ID_SUBMENU" name="ID_SUBMENU" class="form-select mr-sm-2" required>
                                            </select>
                                            <label for="ID_SUBMENU">Sub-Menu</label>
                                            <small id="ID_SUBMENU" class="invalid-feedback">Favor agregar el
                                                Sub-Menu</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select id="ddl_ID_GRILLA" name="ID_GRILLA" class="form-select mr-sm-2">
                                            </select>
                                            <label for="ID_GRILLA">Grilla</label>
                                            <small id="ID_GRILLA" class="invalid-feedback">Favor agregar la
                                                Grilla</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select id="ddl_ID_TABLA" name="ID_TABLA" class="form-select mr-sm-2"></select>
                                            <label for="ID_TABLA">Tabla Abm</label>
                                            <small id="ID_TABLA" class="invalid-feedback">Favor agregar la Tabla
                                                Abm</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select name="ACTIVO" class="form-select mr-sm-2" required>
                                                <option value="1" selected>Activo</option>
                                                <option value="0">Inactivo</option>
                                            </select>
                                            <label for="ACTIVO">Estado</label>
                                            <small id="ACTIVO" class="invalid-feedback">Favor agregar el Estado</small>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        Cerrar
                    </button>
                    <button id="btnguardar" type="button" onclick="Funcionabm.guardarabm()"
                        class=" btn btn-light-info text-info font-weight-medium">
                        Guardar
                    </button>
                </div>
            </div>
        </div>
    </div>
    
<script src="../../wwwroot/formularios/formularios.js"></script>