import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import helmet from 'helmet';
import path from 'path';
import cookieParser from 'cookie-parser';
import router from './routes/router.mjs';
import expressLayauts from 'express-ejs-layouts';
import dotenv from 'dotenv';
import { imageProtectionMiddleware, htmlProtectionMiddleware } from './middleware/imageProtectionMiddleware.mjs';


dotenv.config();

const app = express();
const PORT = process.env.APP_PORT || 3000;
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';
console.log('FRONTEND_URL:', FRONTEND_URL);
console.log('PORT:', PORT);

app.use(cors({
  origin: true, // Permitir TODOS los orígenes
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers'
  ],
  exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
  preflightContinue: false,
  optionsSuccessStatus: 200
}));
//configura los valores del cookies
app.use(cookieParser());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

app.set('view engine', 'ejs');
app.use(expressLayauts);
app.set("layout login", false);
app.set('views', path.resolve(process.cwd(), 'src/views'));
// app.use(express.static(path.join(__dirname, 'views'), { index: false }));

// Aplicar middleware de protección de imágenes antes de servir archivos estáticos
app.use(imageProtectionMiddleware);
app.use(htmlProtectionMiddleware);
app.use(express.static(path.join(process.cwd(), 'public'), { index: false }));


app.use(router);

app.use((error, req, res, next) => {
 
  if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
    console.error('Error de conexión con el backend:', process.env.BACKEND_URL);
    return res.status(500).json({
      success: false,
      message: 'Error de conexión con el servidor backend',
      error: 'Backend no disponible',
      details: `No se pudo conectar con ${process.env.BACKEND_URL}`
    });
  }

  // Error genérico del servidor
  res.status(500).json({
    success: false,
    message: 'Error interno del servidor',
    error: error.message,
    details: process.env.NODE_ENV === 'development' ? error.stack : 'Error interno'
  });
});

app.listen(PORT, '0.0.0.0', () =>{
  console.log(`Servidor corriendo en http://localhost:${PORT}`);
  console.log(`También accesible desde: https://staff.rayco.com.py`);

})
