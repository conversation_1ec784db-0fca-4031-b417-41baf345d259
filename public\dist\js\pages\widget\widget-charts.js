$(function () {
  "use strict";
  // ==============================================================
  // Total revenue chart
  // ==============================================================
  new Chartist.Line(
    ".total-revenue4",
    {
      labels: ["0", "4", "8", "12", "16", "20", "24", "30"],
      series: [
        [0, 2, 3.5, 0, 13, 1, 4, 1],
        [0, 4, 0, 4, 0, 4, 0, 4],
      ],
    },
    {
      high: 15,
      low: 0,
      showArea: true,
      fullWidth: true,
      plugins: [Chartist.plugins.tooltip()], // As this is axis specific we need to tell Chartist to use whole numbers only on the concerned axis
      axisY: {
        onlyInteger: true,
        offset: 20,
        labelInterpolationFnc: function (value) {
          return value / 1 + "k";
        },
      },
    }
  );
  // ==============================================================
  // User analytics
  // ==============================================================
  new Chartist.Line(
    ".user-analytics",
    {
      labels: [
        "10 jan",
        "15 jan",
        "20 jan",
        "25 jan",
        "30 jan",
        "05 Feb",
        "10 Feb",
      ],
      series: [[0, 2, 3.5, 0, 13, 1, 4]],
    },
    {
      high: 25,
      low: 0,
      showArea: true,
      lineSmooth: Chartist.Interpolation.simple({
        divisor: 10,
      }),
      fullWidth: true,
      chartPadding: {
        top: 15,
        right: 20,
        bottom: 5,
        left: 10,
      },
      plugins: [Chartist.plugins.tooltip()], // As this is axis specific we need to tell Chartist to use whole numbers only on the concerned axis
      axisY: {
        onlyInteger: true,
        offset: 20,
        labelInterpolationFnc: function (value) {
          return value / 1 + "k";
        },
      },
    }
  );
  // ==============================================================
  // Realtime chart
  // ==============================================================
  var data = [],
    totalPoints = 300;

  function getRandomData() {
    if (data.length > 0) data = data.slice(1);
    // Do a random walk
    while (data.length < totalPoints) {
      var prev = data.length > 0 ? data[data.length - 1] : 50,
        y = prev + Math.random() * 10 - 5;
      if (y < 0) {
        y = 0;
      } else if (y > 100) {
        y = 100;
      }
      data.push(y);
    }
    // Zip the generated y values with the x values
    var res = [];
    for (var i = 0; i < data.length; ++i) {
      res.push([i, data[i]]);
    }
    return res;
  }
  // Set up the control widget
  var updateInterval = 250;
  $("#updateInterval")
    .val(updateInterval)
    .change(function () {
      var v = $(this).val();
      if (v && !isNaN(+v)) {
        updateInterval = +v;
        if (updateInterval < 1) {
          updateInterval = 1;
        } else if (updateInterval > 3000) {
          updateInterval = 3000;
        }
        $(this).val("" + updateInterval);
      }
    });
  var plot = $.plot("#placeholder", [getRandomData()], {
    series: {
      shadowSize: 0, // Drawing is faster without shadows
    },
    yaxis: {
      min: 0,
      max: 100,
    },
    xaxis: {
      show: false,
    },
    colors: ["#26c6da"],
    grid: {
      color: "#AFAFAF",
      hoverable: true,
      borderWidth: 0,
      backgroundColor: "transparent",
    },
    tooltip: true,
    tooltipOpts: {
      content: "Visit: %y",
      defaultTheme: false,
    },
  });
  $(window).resize(function () {
    $.plot($("#placeholder"), data);
  });

  function update() {
    plot.setData([getRandomData()]);
    // Since the axes don't change, we don't need to call plot.setupGrid()
    plot.draw();
    setTimeout(update, updateInterval);
  }
  update();
  // ==============================================================
  // Android vs IOS
  // ==============================================================
  new Chartist.Line(
    ".andvios",
    {
      labels: [
        "0",
        "4",
        "8",
        "12",
        "16",
        "20",
        "24",
        "30",
        "16",
        "20",
        "24",
        "30",
        "34",
        "38",
        "42",
        "46",
        "50",
        "54",
      ],
      series: [
        [11, 4, 3, 14, 9, 10, 18, 15, 24, 17, 19, 26, 31, 26, 37, 41, 46, 51],
        [8, 1, 1, 10, 11, 6, 12, 14, 21, 15, 21, 24, 28, 23, 34, 38, 41, 47],
      ],
    },
    {
      low: 0,
      showArea: true,
      fullWidth: true,
      chartPadding: 0,
      axisX: {
        showLabel: false,
        divisor: 2,
        showGrid: false,
        offset: 0,
      },
      plugins: [Chartist.plugins.tooltip()], // As this is axis specific we need to tell Chartist to use whole numbers only on the concerned axis
      axisY: {
        onlyInteger: true,
        showLabel: false,
        offset: 0,
      },
    }
  );
  // ==============================================================
  // Badnwidth usage
  // ==============================================================
  new Chartist.Line(
    ".usage",
    {
      labels: ["0", "4", "8", "12", "16", "20", "24", "30"],
      series: [[5, 0, 12, 1, 8, 3, 12, 15]],
    },
    {
      high: 13,
      low: 0,
      showArea: true,
      fullWidth: true,
      plugins: [Chartist.plugins.tooltip()], // As this is axis specific we need to tell Chartist to use whole numbers only on the concerned axis
      axisY: {
        onlyInteger: true,
        offset: 20,
        showLabel: false,
        showGrid: false,
        labelInterpolationFnc: function (value) {
          return value / 1 + "k";
        },
      },
      axisX: {
        showLabel: false,
        divisor: 2,
        showGrid: false,
        offset: 0,
      },
    }
  );
  // ==============================================================
  // Download count
  // ==============================================================
  var sparklineLogin = function () {
    $(".spark-count").sparkline(
      [4, 5, 0, 10, 9, 12, 4, 9, 4, 5, 3, 10, 9, 12, 10, 9, 12, 4, 9],
      {
        type: "bar",
        width: "100%",
        height: "100",
        barWidth: "8",
        resize: true,
        barSpacing: "5",
        barColor: "rgba(255, 255, 255, 0.3)",
      }
    );
    $("#spark1").sparkline([2, 4, 4, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#26c6da",
      fillColor: "#26c6da",
      maxSpotColor: "#26c6da",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#26c6da",
    });
    $("#spark2").sparkline([0, 2, 8, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#009efb",
      fillColor: "#009efb",
      minSpotColor: "#009efb",
      maxSpotColor: "#009efb",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#009efb",
    });
    $("#spark3").sparkline([2, 4, 4, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#7460ee",
      fillColor: "#7460ee",
      maxSpotColor: "#7460ee",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#7460ee",
    });
    $("#spark4").sparkline([2, 4, 4, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#fff",
      fillColor: "#7460ee",
      maxSpotColor: "#7460ee",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#7460ee",
    });
    $("#spark5").sparkline([2, 4, 4, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#fff",
      fillColor: "#1e88e5",
      maxSpotColor: "#1e88e5",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#1e88e5",
    });
    $("#spark6").sparkline([2, 4, 4, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#fff",
      fillColor: "#21c1d6",
      maxSpotColor: "#21c1d6",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#21c1d6",
    });
    $("#spark7").sparkline([2, 4, 4, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#fff",
      fillColor: "#ffb22b",
      maxSpotColor: "#ffb22b",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#ffb22b",
    });
    $("#spark8").sparkline([4, 5, 0, 10, 9, 12, 4, 9], {
      type: "bar",
      width: "100%",
      height: "70",
      barWidth: "8",
      resize: true,
      barSpacing: "5",
      barColor: "#26c6da",
    });
    $("#spark9").sparkline([0, 5, 6, 10, 9, 12, 4, 9], {
      type: "bar",
      width: "100%",
      height: "70",
      barWidth: "8",
      resize: true,
      barSpacing: "5",
      barColor: "#7460ee",
    });
    $("#spark10").sparkline([0, 5, 6, 10, 9, 12, 4, 9], {
      type: "bar",
      width: "100%",
      height: "70",
      barWidth: "8",
      resize: true,
      barSpacing: "5",
      barColor: "#03a9f3",
    });
    $("#spark11").sparkline([0, 5, 6, 10, 9, 12, 4, 9], {
      type: "bar",
      width: "100%",
      height: "70",
      barWidth: "8",
      resize: true,
      barSpacing: "5",
      barColor: "#f62d51",
    });
    $("#sparklinedash").sparkline([0, 5, 6, 10, 9, 12, 4, 9], {
      type: "bar",
      height: "50",
      barWidth: "2",
      resize: true,
      barSpacing: "5",
      barColor: "#ffb22b",
    });
    $("#sparklinedash2").sparkline([0, 5, 6, 10, 9, 12, 4, 9], {
      type: "bar",
      height: "50",
      barWidth: "2",
      resize: true,
      barSpacing: "5",
      barColor: "#f64e60",
    });
    $("#sparklinedash3").sparkline([0, 5, 6, 10, 9, 12, 4, 9], {
      type: "bar",
      height: "50",
      barWidth: "2",
      resize: true,
      barSpacing: "5",
      barColor: "#6993ff",
    });
    $("#sparklinedash4").sparkline([0, 5, 6, 10, 9, 12, 4, 9], {
      type: "bar",
      height: "50",
      barWidth: "2",
      resize: true,
      barSpacing: "5",
      barColor: "#1e88e5",
    });
    $("#sparkline8").sparkline([2, 4, 4, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#6993ff",
      fillColor: "#6993ff",
      maxSpotColor: "#6993ff",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#6993ff",
    });
    $("#sparkline9").sparkline([0, 2, 8, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#1bc5bd",
      fillColor: "#1bc5bd",
      minSpotColor: "#1bc5bd",
      maxSpotColor: "#1bc5bd",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#1bc5bd",
    });
    $("#sparkline10").sparkline([2, 4, 4, 6, 8, 5, 6, 4, 8, 6, 6, 2], {
      type: "line",
      width: "100%",
      height: "50",
      lineColor: "#f64e60",
      fillColor: "#f64e60",
      maxSpotColor: "#f64e60",
      highlightLineColor: "rgba(0, 0, 0, 0.2)",
      highlightSpotColor: "#f64e60",
    });
  };
  var sparkResize;
  $(window).resize(function (e) {
    clearTimeout(sparkResize);
    sparkResize = setTimeout(sparklineLogin, 500);
  });
  sparklineLogin();
  // ==============================================================
  // Download count
  // ==============================================================
  new Chartist.Bar(
    ".download-state",
    {
      labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      series: [
        [5, 4, 3, 7, 5, 10, 3],
        [3, 2, 9, 5, 4, 6, 4],
      ],
    },
    {
      high: 11,
      low: 0,
      showArea: true,
      seriesBarDistance: 10,
      fullWidth: true,
      plugins: [Chartist.plugins.tooltip()],
      axisX: {
        // On the x-axis start means top and end means bottom
        showGrid: false,
      },
    },
    {}
  );

  // ==============================================================
  // Our Visitor
  // ==============================================================

  var chart = c3.generate({
    bindto: "#visitor",
    data: {
      columns: [
        ["Open", 45],
        ["Clicked", 15],
        ["Un-opened", 27],
        ["Bounced", 18],
      ],

      type: "donut",
      tooltip: {
        show: true,
      },
    },
    donut: {
      label: {
        show: false,
      },
      title: "Ratio",
      width: 35,
    },

    legend: {
      hide: true,
      //or hide: 'data1'
      //or hide: ['data1', 'data2']
    },
    color: {
      pattern: ["#40c4ff", "#2961ff", "#ff821c", "#7e74fb"],
    },
  });

  // ==============================================================
  // Foo1 total visit
  // ==============================================================
  var opts = {
    angle: 0, // The span of the gauge arc
    lineWidth: 0.2, // The line thickness
    radiusScale: 0.7, // Relative radius
    pointer: {
      length: 0.64, // // Relative to gauge radius
      strokeWidth: 0.04, // The thickness
      color: "#000000", // Fill color
    },
    limitMax: false, // If false, the max value of the gauge will be updated if value surpass max
    limitMin: false, // If true, the min value of the gauge will be fixed unless you set it manually
    colorStart: "#009efb", // Colors
    colorStop: "#009efb", // just experiment with them
    strokeColor: "#E0E0E0", // to see which ones work best for you
    generateGradient: true,
    highDpiSupport: true, // High resolution support
  };
  var target = document.getElementById("foo"); // your canvas element
  var gauge = new Gauge(target).setOptions(opts); // create sexy gauge!
  gauge.maxValue = 3000; // set max gauge value
  gauge.setMinValue(0); // Prefer setter over gauge.minValue = 0
  gauge.animationSpeed = 45; // set animation speed (32 is default value)
  gauge.set(1850); // set actual value
  // ==============================================================
  // Foo1 total visit
  // ==============================================================
  var opts = {
    angle: 0, // The span of the gauge arc
    lineWidth: 0.2, // The line thickness
    radiusScale: 0.7, // Relative radius
    pointer: {
      length: 0.64, // // Relative to gauge radius
      strokeWidth: 0.04, // The thickness
      color: "#000000", // Fill color
    },
    limitMax: false, // If false, the max value of the gauge will be updated if value surpass max
    limitMin: false, // If true, the min value of the gauge will be fixed unless you set it manually
    colorStart: "#7460ee", // Colors
    colorStop: "#7460ee", // just experiment with them
    strokeColor: "#E0E0E0", // to see which ones work best for you
    generateGradient: true,
    highDpiSupport: true, // High resolution support
  };
  var target = document.getElementById("foo2"); // your canvas element
  var gauge = new Gauge(target).setOptions(opts); // create sexy gauge!
  gauge.maxValue = 3000; // set max gauge value
  gauge.setMinValue(0); // Prefer setter over gauge.minValue = 0
  gauge.animationSpeed = 45; // set animation speed (32 is default value)
  gauge.set(850); // set actual value
  // ==============================================================
  // Foo1 total visit
  // ==============================================================
  var opts = {
    angle: 0, // The span of the gauge arc
    lineWidth: 0.2, // The line thickness
    radiusScale: 0.7, // Relative radius
    pointer: {
      length: 0.64, // // Relative to gauge radius
      strokeWidth: 0.04, // The thickness
      color: "#000000", // Fill color
    },
    limitMax: false, // If false, the max value of the gauge will be updated if value surpass max
    limitMin: false, // If true, the min value of the gauge will be fixed unless you set it manually
    colorStart: "#f62d51", // Colors
    colorStop: "#f62d51", // just experiment with them
    strokeColor: "#E0E0E0", // to see which ones work best for you
    generateGradient: true,
    highDpiSupport: true, // High resolution support
  };
  var target = document.getElementById("foo3"); // your canvas element
  var gauge = new Gauge(target).setOptions(opts); // create sexy gauge!
  gauge.maxValue = 3000; // set max gauge value
  gauge.setMinValue(0); // Prefer setter over gauge.minValue = 0
  gauge.animationSpeed = 45; // set animation speed (32 is default value)
  gauge.set(1250); // set actual value

  // ==============================================================
  // Foo1 total visit
  // ==============================================================
  var opts = {
    angle: 0, // The span of the gauge arc
    lineWidth: 0.2, // The line thickness
    radiusScale: 0.7, // Relative radius
    pointer: {
      length: 0.64, // // Relative to gauge radius
      strokeWidth: 0.04, // The thickness
      color: "#000000", // Fill color
    },
    limitMax: false, // If false, the max value of the gauge will be updated if value surpass max
    limitMin: false, // If true, the min value of the gauge will be fixed unless you set it manually
    colorStart: "#26c6da", // Colors
    colorStop: "#26c6da", // just experiment with them
    strokeColor: "#E0E0E0", // to see which ones work best for you
    generateGradient: true,
    highDpiSupport: true, // High resolution support
  };
  var target = document.getElementById("foo4"); // your canvas element
  var gauge = new Gauge(target).setOptions(opts); // create sexy gauge!
  gauge.maxValue = 3000; // set max gauge value
  gauge.setMinValue(0); // Prefer setter over gauge.minValue = 0
  gauge.animationSpeed = 45; // set animation speed (32 is default value)
  gauge.set(2850); // set actual value
});
