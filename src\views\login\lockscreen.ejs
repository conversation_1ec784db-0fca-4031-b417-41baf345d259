<!DOCTYPE html>
<html dir="ltr" lang="es">

<head>

    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- Tell the browser to be responsive to screen width -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="keywords" content="Webapp" />
    <meta name="description" content="RayCo." />
    <meta name="robots" content="noindex,nofollow" />
    <title><%= process.env.APP_TITLE || 'Staff Ray Co.' %></title>
    <link rel="icon" type="image/png" sizes="<%= process.env.FAVICON_SIZE || '16x16' %>" href="/assets/images/marcas/raycoLogoR.png"/>
    <link href="/dist/css/style.min.css" rel="stylesheet" />
    <link href="/dist/libs/fontawesomepro/css/all.min.css" rel="stylesheet" />
  <style>
    .profile-picture-lc {
      border-radius: 50%;
      background-color: #000;
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      font-weight: bold;
      text-transform: uppercase;
    }

    @media (max-width: 768px) {
      .decorative-image-left {
        top: 5% !important;
        left: 5% !important;
        padding-left: 5px !important;
      }

      .decorative-image-left img {
        width: 120px !important;
      }

      .decorative-image-right {
        bottom: 5% !important;
        right: 5% !important;
        padding-right: 5px !important;
      }

      .decorative-image-right img {
        width: 100px !important;
      }
    }

    @media (max-width: 480px) {
      .decorative-image-left {
        top: 5% !important;
        left: 5% !important;
        padding-left: 5px !important;
      }

      .decorative-image-left img {
        width: 80px !important;
      }

      .decorative-image-right {
        bottom: 5% !important;
        right: 5% !important;
        padding-right: 5px !important;
      }

      .decorative-image-right img {
        width: 70px !important;
      }
    }
  </style>
</head>
<script>
  const BASE_URL = `${location.protocol}//${location.host}`; // fallback automático
</script>

<body>
  <div class="main-wrapper">
    <%- include('../../components/preloader.ejs') %>

    <div class="main-wrapper" style="background-color: #f5f5f5; min-height: 100vh;">
      <div class="auth-wrapper d-flex no-block justify-content-center align-items-center position-relative" style="min-height: 100vh;">

        <div class="auth-box on-sidebar p-4 bg-white m-0 shadow-lg" style="z-index: 10; border-radius: 15px; position: relative;">
          <div>
            <div class="logo text-center">
              <span class="db">
                <img alt="user" id="usuPerfil" class="rounded-circle" width="60" src="../../assets/images/users/profile.png" style="display: none;">
                <div id="usuPerfil_str" class="rounded-circle profile-picture-lc" style="display: none; width: 80px; height: 80px; margin: 0 auto;">
                  <!-- Iniciales del usuario -->
                </div>
              </span>
              <h5 class="font-weight-medium mb-3" id="usuNombre_menu">Cargando...</h5>
            </div>
            <!-- Form -->
            <div class="row">
              <div class="col-12">
                <form class="form-horizontal mt-3" action="/lockscreen" method="POST">
                  <div class="mb-3 row">
                    <div class="col-12">
                      <input class="form-control" type="text" name="USUARIO" id="USUARIO" placeholder="Nro. Documento" style="display:none;" />
                      <input class="form-control" type="password" required="" name="CLAVE_ACCESO" placeholder="Contraseña" />
                    </div>
                  </div>
                  <div class="text-center">
                    <div class="col-xs-12">
                      <button class="btn d-block w-100 btn-info" style="margin-bottom:5px;" type="submit">Ingresar</button>
                      <a class="btn d-block w-100 btn-danger" href="/logout">Salir</a>
                    </div>  
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="../../dist/libs/jquery/dist/jquery.min.js"></script>
  <!-- Bootstrap tether Core JavaScript -->
  <script src="../../dist/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="../../wwwroot/funciones_gral/funciones_gral.js"></script>
  <script src="../../wwwroot/login/lockscreen.js"></script>
  <script>
    $(".preloader").fadeOut();

    $(document).ready(function() {

      let session = getCookieValue("session");
      if (session) {
        window.session = session;
        const event = new CustomEvent('sessionReady');
        document.dispatchEvent(event);
      }
    });
  </script>
</body>

</html>