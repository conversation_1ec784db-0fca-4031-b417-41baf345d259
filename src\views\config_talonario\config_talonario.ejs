
<div class="row">
    <div class="col-md-4"> 
        <div class="form-floating mb-2">
            <select id="ID_TIPO_COMPROBANTE" class="form-select mr-sm-2" required>
            </select>
            <label for="ID_TIPO_COMPROBANTE">Tipo Comprobante</label>
            <small id="ID_TIPO_COMPROBANTE_alert" class="invalid-feedback">
                Favor agregar el Tipo Comprobante
            </small>
        </div>
    </div>
    <div class="col-md-4"> 
        <div class="form-floating mb-2">
            <select id="ID_TALONARIO" class="form-select mr-sm-2" required>
            </select>
            <label for="ID_TALONARIO">Talonario</label>
            <small id="ID_TALONARIO_alert" class="invalid-feedback">
                Favor agregar el Tipo Comprobante
            </small>
        </div>
    </div>
    <div class="col-md-4"> 
        <div class="form-group mb-6">
            <label id="lblNumero">Número Comprobante</label>
            <label id="lblNumeracionAutomatica" style="font-size: 10px; display:none"></label>
            <div class="row ms-2 me-1">
                <div class="col-md-3 col-3 px-1">
                    <input type="text" id="PUNTO_EXP1" class="form-control" maxlength="1" onKeypress="if (event.keyCode < 45 || event.keyCode > 57) event.returnValue = false;">
                </div>
                <div class="col-md-3 col-3 px-1">
                    <input type="text" id="PUNTO_EXP2" class="form-control" maxlength="3" onKeypress="if (event.keyCode < 45 || event.keyCode > 57) event.returnValue = false;">
                </div>
                <div class="col-md-6 col-6 px-1">
                    <input type="text" id="NRO_COMPROBANTE" class="form-control" maxlength="7" onKeypress="if (event.keyCode < 45 || event.keyCode > 57) event.returnValue = false;">
                </div>

            </div>
        </div>    
    </div>
</div>
<script src="../../wwwroot/config_talonario/config_talonariov2.js"></script>