$(function () {
  "use strict";
  // ------------------------------
  // Basic line chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var myChart = echarts.init(document.getElementById("basic-line"));

  // specify chart configuration item and data
  var option = {
    // Setup grid
    grid: {
      left: "1%",
      right: "2%",
      bottom: "3%",
      containLabel: true,
    },

    // Add Tooltip
    tooltip: {
      trigger: "axis",
    },

    // Add Legend
    legend: {
      data: ["Max temp", "Min temp"],
    },

    // Add custom colors
    color: ["#1e88e5", "#f62d51"],

    // Enable drag recalculate
    calculable: true,

    // Horizontal Axiz
    xAxis: [
      {
        type: "category",
        boundaryGap: false,
        data: ["Mon", "<PERSON>e", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "Sun"],
        color: "#efefef",
      },
    ],

    // Vertical Axis
    yAxis: [
      {
        type: "value",
        axisLabel: {
          formatter: "{value} °C",
        },
      },
    ],

    // Add Series
    series: [
      {
        name: "Max temp",
        type: "line",
        data: [5, 15, 11, 15, 12, 13, 10],
        markPoint: {
          data: [
            { type: "max", name: "Max" },
            { type: "min", name: "Min" },
          ],
        },
        markLine: {
          data: [{ type: "average", name: "Average" }],
        },
        lineStyle: {
          normal: {
            width: 3,
            shadowColor: "rgba(0,0,0,0.1)",
            shadowBlur: 10,
            shadowOffsetY: 10,
          },
        },
      },
      {
        name: "Min temp",
        type: "line",
        data: [1, -2, 2, 5, 3, 2, 0],
        markPoint: {
          data: [{ name: "Week low", value: -2, xAxis: 1, yAxis: -1.5 }],
        },
        markLine: {
          data: [{ type: "average", name: "Average" }],
        },
        lineStyle: {
          normal: {
            width: 3,
            shadowColor: "rgba(0,0,0,0.1)",
            shadowBlur: 10,
            shadowOffsetY: 10,
          },
        },
      },
    ],
  };
  // use configuration item and data specified to show chart
  myChart.setOption(option);

  // ------------------------------
  // Basic line chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var bareaChart = echarts.init(document.getElementById("basic-area"));

  // specify chart configuration item and data
  var option = {
    // Setup grid
    grid: {
      left: "1%",
      right: "2%",
      bottom: "3%",
      containLabel: true,
    },

    // Add Tooltip
    tooltip: {
      trigger: "axis",
    },

    // Add Legend
    legend: {
      data: ["Max temp", "Min temp"],
    },

    // Add custom colors
    color: ["#1e88e5", "#00acc1"],

    // Enable drag recalculate
    calculable: true,

    // Horizontal Axiz
    xAxis: [
      {
        type: "category",
        boundaryGap: false,
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
    ],

    // Vertical Axis
    yAxis: [
      {
        type: "value",
        axisLabel: {
          formatter: "{value} °C",
        },
      },
    ],

    // Add Series
    series: [
      {
        name: "Max temp",
        type: "line",
        areaStyle: {},
        data: [5, 15, 11, 15, 12, 13, 10],
        markPoint: {
          data: [
            { type: "max", name: "Max" },
            { type: "min", name: "Min" },
          ],
        },
        markLine: {
          data: [{ type: "average", name: "Average" }],
        },
      },
      {
        name: "Min temp",
        type: "line",
        areaStyle: {},
        data: [1, 8, 2, 5, 3, 2, 0],
        markPoint: {
          data: [{ name: "Week low", value: -2, xAxis: 1, yAxis: -1.5 }],
        },
        markLine: {
          data: [{ type: "average", name: "Average" }],
        },
      },
    ],
  };
  // use configuration item and data specified to show chart
  bareaChart.setOption(option);

  //***************************
  // Stacked chart
  //***************************
  var stackedChart = echarts.init(document.getElementById("stacked-line"));
  var option = {
    grid: {
      left: "1%",
      right: "2%",
      bottom: "3%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
    },
    // Add legend
    legend: {
      data: [
        "Elite admin",
        "Monster admin",
        "Ample admin",
        "Material admin",
        "Angular admin",
      ],
    },

    // Add custom colors
    color: ["#1e88e5", "#7460ee", "#f62d51", "#36bea6", "#212529"],

    // Enable drag recalculate
    calculable: true,

    // Hirozontal axis
    xAxis: [
      {
        type: "category",
        boundaryGap: false,
        data: [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
          "Sunday",
        ],
      },
    ],

    // Vertical axis
    yAxis: [
      {
        type: "value",
      },
    ],

    // Add series
    series: [
      {
        name: "Elite admin",
        type: "line",
        stack: "Total",
        data: [120, 132, 101, 134, 90, 230, 210],
      },
      {
        name: "Monster admin",
        type: "line",
        stack: "Total",
        data: [220, 182, 191, 234, 290, 330, 310],
      },
      {
        name: "Ample admin",
        type: "line",
        stack: "Total",
        data: [150, 232, 201, 154, 190, 330, 410],
      },
      {
        name: "Material admin",
        type: "line",
        stack: "Total",
        data: [320, 332, 301, 334, 390, 330, 320],
      },
      {
        name: "Angular admin",
        type: "line",
        stack: "Total",
        data: [820, 932, 901, 934, 1290, 1330, 1320],
      },
    ],
    // Add series
  };
  stackedChart.setOption(option);

  //***************************
  // Stacked Area chart
  //***************************
  var stackedareaChart = echarts.init(document.getElementById("stacked-area"));
  var option = {
    grid: {
      left: "1%",
      right: "2%",
      bottom: "3%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
    },
    // Add legend
    legend: {
      data: [
        "Elite admin",
        "Monster admin",
        "Ample admin",
        "Material admin",
        "Angular admin",
      ],
    },

    // Add custom colors
    color: ["#212529", "#7460ee", "#f62d51", "#36bea6", "#1e88e5"],

    // Enable drag recalculate
    calculable: true,

    // Hirozontal axis
    xAxis: [
      {
        type: "category",
        boundaryGap: false,
        data: [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
          "Sunday",
        ],
      },
    ],

    // Vertical axis
    yAxis: [
      {
        type: "value",
      },
    ],

    // Add series
    series: [
      {
        name: "Elite admin",
        type: "line",
        stack: "Total",
        areaStyle: {},
        data: [120, 132, 101, 134, 90, 230, 210],
      },
      {
        name: "Monster admin",
        type: "line",
        stack: "Total",
        areaStyle: {},
        data: [220, 182, 191, 234, 290, 330, 310],
      },
      {
        name: "Ample admin",
        type: "line",
        stack: "Total",
        areaStyle: {},
        data: [150, 232, 201, 154, 190, 330, 410],
      },
      {
        name: "Material admin",
        type: "line",
        stack: "Total",
        areaStyle: {},
        data: [320, 332, 301, 334, 390, 330, 320],
      },
      {
        name: "Angular admin",
        type: "line",
        stack: "Total",
        areaStyle: {},
        data: [820, 932, 901, 934, 1290, 1330, 1320],
      },
    ],
    // Add series
  };
  stackedareaChart.setOption(option);

  // ------------------------------
  // Basic line chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var gradiantChart = echarts.init(document.getElementById("g-line"));

  // specify chart configuration item and data
  var data = [
    ["2000-06-05", 116],
    ["2000-06-06", 129],
    ["2000-06-07", 135],
    ["2000-06-08", 86],
    ["2000-06-09", 73],
    ["2000-06-10", 85],
    ["2000-06-11", 73],
    ["2000-06-12", 68],
    ["2000-06-13", 92],
    ["2000-06-14", 130],
    ["2000-06-15", 245],
    ["2000-06-16", 139],
    ["2000-06-17", 115],
    ["2000-06-18", 111],
    ["2000-06-19", 309],
    ["2000-06-20", 206],
    ["2000-06-21", 137],
    ["2000-06-22", 128],
    ["2000-06-23", 85],
    ["2000-06-24", 94],
    ["2000-06-25", 71],
    ["2000-06-26", 106],
    ["2000-06-27", 84],
    ["2000-06-28", 93],
    ["2000-06-29", 85],
    ["2000-06-30", 73],
    ["2000-07-01", 83],
    ["2000-07-02", 125],
    ["2000-07-03", 107],
    ["2000-07-04", 82],
    ["2000-07-05", 44],
    ["2000-07-06", 72],
    ["2000-07-07", 106],
    ["2000-07-08", 107],
    ["2000-07-09", 66],
    ["2000-07-10", 91],
    ["2000-07-11", 92],
    ["2000-07-12", 113],
    ["2000-07-13", 107],
    ["2000-07-14", 131],
    ["2000-07-15", 111],
    ["2000-07-16", 64],
    ["2000-07-17", 69],
    ["2000-07-18", 88],
    ["2000-07-19", 77],
    ["2000-07-20", 83],
    ["2000-07-21", 111],
    ["2000-07-22", 57],
    ["2000-07-23", 55],
    ["2000-07-24", 60],
  ];

  var dateList = data.map(function (item) {
    return item[0];
  });
  var valueList = data.map(function (item) {
    return item[1];
  });

  var option = {
    // Make gradient line here
    visualMap: [
      {
        show: false,
        type: "continuous",
        seriesIndex: 0,
        min: 0,
        max: 400,
      },
      {
        show: false,
        type: "continuous",
        seriesIndex: 1,
        dimension: 0,
        min: 0,
        max: dateList.length - 1,
      },
    ],

    title: [
      {
        left: "center",
        text: "Gradient along the y axis",
      },
      {
        top: "55%",
        left: "center",
        text: "Gradient along the x axis",
      },
    ],
    tooltip: {
      trigger: "axis",
    },

    xAxis: [
      {
        data: dateList,
      },
      {
        data: dateList,
        gridIndex: 1,
      },
    ],
    yAxis: [
      {
        splitLine: { show: false },
      },
      {
        splitLine: { show: false },
        gridIndex: 1,
      },
    ],
    grid: [
      {
        bottom: "60%",
        left: "3%",
        right: "3%",
      },
      {
        top: "60%",
        left: "3%",
        right: "3%",
      },
    ],

    series: [
      {
        type: "line",
        showSymbol: false,
        data: valueList,
      },
      {
        type: "line",
        showSymbol: false,
        data: valueList,
        xAxisIndex: 1,
        yAxisIndex: 1,
      },
    ],
  };
  // use configuration item and data specified to show chart
  gradiantChart.setOption(option);

  //------------------------------------------------------
  // Resize chart on menu width change and window resize
  //------------------------------------------------------
  $(function () {
    // Resize chart on menu width change and window resize
    $(window).on("resize", resize);
    $(".sidebartoggler").on("click", resize);

    // Resize function
    function resize() {
      setTimeout(function () {
        // Resize chart
        myChart.resize();
        bareaChart.resize();
        stackedChart.resize();
        stackedareaChart.resize();
        gradiantChart.resize();
      }, 200);
    }
  });
});
