.alert {
  &.customize-alert {
    padding: 20px 25px;
    .btn-close {
      width: 25px;
    }
    .side-line {
      position: absolute;
      width: 3px;
      height: 25px;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

@include media-breakpoint-down(sm) {
  .alert {
    &.customize-alert {
      padding: 20px 15px;
    }
  }
}

.toast:not(.showing):not(.show) {
  opacity: 1;
  display: block;
}

.toast:not(.showing):not(.show) {
  opacity: 1 !important;
  display: none;
}
