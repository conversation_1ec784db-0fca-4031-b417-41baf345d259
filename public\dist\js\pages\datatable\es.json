{"aria": {"sortAscending": "Activar para ordenar la columna de manera ascendente", "sortDescending": "Activar para ordenar la columna de manera descendente"}, "autoFill": {"cancel": "<PERSON><PERSON><PERSON>", "fill": "Rellene todas las celdas con <i>%d</i>", "fillHorizontal": "Rellenar celdas horizontalmente", "fillVertical": "Rellenar celdas verticalmente"}, "buttons": {"collection": "Colección", "colvis": "Visibilidad", "colvisRestore": "Restaurar visibilidad", "copy": "Copiar", "copyKeys": "Presione ctrl o u2318 + C para copiar los datos de la tabla al portapapeles del sistema. <br /> <br /> Para cancelar, haga clic en este mensaje o presione escape.", "copySuccess": {"1": "Copiada 1 fila al portapapeles", "_": "Copiadas %d fila al portapapeles"}, "copyTitle": "Copiar al portapapeles", "csv": "CSV", "excel": "Excel", "pageLength": {"-1": "Mostrar todas las filas", "_": "Mostrar %d filas"}, "pdf": "PDF", "print": "Imprimir", "createState": "<PERSON><PERSON><PERSON>", "removeAllStates": "Borrar Todos los Estados", "removeState": "<PERSON><PERSON><PERSON>", "renameState": "Renombrar <PERSON>", "savedStates": "Guardar E<PERSON>o", "stateRestore": "<PERSON><PERSON><PERSON>", "updateState": "<PERSON><PERSON><PERSON><PERSON>"}, "infoThousands": ",", "loadingRecords": "Cargando...", "paginate": {"first": "Primero", "last": "Último", "next": "<PERSON><PERSON><PERSON>.", "previous": "Anterior"}, "processing": "Procesando...", "search": "Buscar:", "searchBuilder": {"add": "Añadir condición", "button": {"0": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "_": "<PERSON>st<PERSON><PERSON> <PERSON> bús<PERSON>da (%d)"}, "clearAll": "<PERSON><PERSON><PERSON> todo", "condition": "Condición", "deleteTitle": "Eliminar regla de filtrado", "leftTitle": "Criterios an<PERSON>", "logicAnd": "Y", "logicOr": "O", "rightTitle": "Criterios de sangría", "title": {"0": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "_": "<PERSON>st<PERSON><PERSON> <PERSON> bús<PERSON>da (%d)"}, "value": "Valor", "conditions": {"date": {"after": "Después", "before": "<PERSON><PERSON>", "between": "<PERSON><PERSON>", "empty": "Vacío", "equals": "Igual a", "not": "Diferente de", "notBetween": "No entre", "notEmpty": "No vacío"}, "number": {"between": "<PERSON><PERSON>", "empty": "Vacío", "equals": "Igual a", "gt": "Mayor a", "gte": "Mayor o igual a", "lt": "<PERSON><PERSON> que", "lte": "<PERSON>or o igual a", "not": "Diferente de", "notBetween": "No entre", "notEmpty": "No vacío"}, "string": {"contains": "<PERSON><PERSON><PERSON>", "empty": "Vacío", "endsWith": "Termina con", "equals": "Igual a", "not": "Diferente de", "startsWith": "Inicia con", "notEmpty": "No vacío", "notContains": "No Contiene", "notEndsWith": "No Termina", "notStartsWith": "No Comienza"}, "array": {"equals": "Igual a", "empty": "Vacío", "contains": "<PERSON><PERSON><PERSON>", "not": "Di<PERSON><PERSON>", "notEmpty": "No vacío", "without": "Sin"}}, "data": "Datos"}, "searchPanes": {"clearMessage": "<PERSON><PERSON><PERSON> todo", "collapse": {"0": "Paneles de búsqueda", "_": "Paneles de búsqueda (%d)"}, "count": "{total}", "emptyPanes": "Sin paneles de búsqueda", "loadMessage": "Cargando paneles de búsqueda", "title": "Filtros Activos - %d", "countFiltered": "{shown} ({total})", "collapseMessage": "Colapsar", "showMessage": "<PERSON><PERSON>"}, "select": {"cells": {"1": "1 celda seleccionada", "_": "%d celdas seleccionadas"}, "columns": {"1": "1 columna seleccionada", "_": "%d columnas seleccionadas"}, "rows": {"1": "1 fila seleccionada", "_": "%d filas seleccionadas"}}, "thousands": ".", "datetime": {"previous": "Anterior", "hours": "<PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "seconds": "<PERSON><PERSON><PERSON>", "unknown": "-", "amPm": ["am", "pm"], "next": "Siguient<PERSON>", "months": {"0": "<PERSON><PERSON>", "1": "<PERSON><PERSON><PERSON>", "10": "Noviembre", "11": "Diciembre", "2": "<PERSON><PERSON>", "3": "Abril", "4": "Mayo", "5": "<PERSON><PERSON>", "6": "<PERSON>", "7": "Agosto", "8": "Septiembre", "9": "Octubre"}, "weekdays": ["Domingo", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Viernes", "Sábado"]}, "editor": {"close": "<PERSON><PERSON><PERSON>", "create": {"button": "Nuevo", "title": "Crear Nuevo Registro", "submit": "<PERSON><PERSON><PERSON>"}, "edit": {"button": "<PERSON><PERSON>", "title": "<PERSON><PERSON>", "submit": "Actualizar"}, "remove": {"button": "Eliminar", "title": "Eliminar Registro", "submit": "Eliminar", "confirm": {"_": "¿Está seguro que desea eliminar %d filas?", "1": "¿Está seguro que desea eliminar 1 fila?"}}, "multi": {"title": "<PERSON><PERSON><PERSON><PERSON>", "restore": "<PERSON><PERSON><PERSON>", "noMulti": "Este registro puede ser editado individualmente, pero no como parte de un grupo.", "info": "Los elementos seleccionados contienen diferentes valores para este registro. Para editar y establecer todos los elementos de este registro con el mismo valor, haga click o toque aquí, de lo contrario conservarán sus valores individuales."}, "error": {"system": "Ha ocurrido un error en el sistema (<a target=\"\\\" rel=\"\\ nofollow\" href=\"\\\"> Más información</a>)."}}, "decimal": ",", "emptyTable": "No hay datos disponibles en la tabla", "zeroRecords": "No se encontraron coincidencias", "info": "Mostrando _START_ a _END_ de _TOTAL_ entradas", "infoFiltered": "(Filtrado de _MAX_ total de entradas)", "lengthMenu": "Mostrar _MENU_ entradas", "stateRestore": {"removeTitle": "Eliminar", "creationModal": {"search": "Buscar", "button": "<PERSON><PERSON><PERSON>", "columns": {"search": "Columna de búsqueda", "visible": "Columna de visibilidad"}, "name": "Nombre:", "order": "Ordenar", "paging": "<PERSON><PERSON><PERSON>", "scroller": "Posición de desplazamiento", "searchBuilder": "<PERSON><PERSON><PERSON>", "select": "Selector", "title": "<PERSON><PERSON><PERSON> nuevo", "toggleLabel": "Incluye:"}, "duplicateError": "Ya existe un valor con el mismo nombre", "emptyError": "No puede ser vacío", "emptyStates": "No se han guardado", "removeConfirm": "Esta seguro de eliminar %s?", "removeError": "Fallo al eliminar", "removeJoiner": "y", "removeSubmit": "Eliminar", "renameButton": "Renombrar", "renameLabel": "Nuevo nombre para %s:", "renameTitle": "Renombrar"}, "infoEmpty": "No hay datos para mostrar"}