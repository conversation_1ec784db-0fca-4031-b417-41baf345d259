$(function () {
  var LIST_COUNTER = 0,
    List = function ($lobiList, options) {
      (this.$lobiList = $lobiList),
        (this.$options = options),
        (this.$globalOptions = $lobiList.$options),
        (this.$items = {}),
        this._init();
    };
  List.prototype = {
    $lobiList: null,
    $el: null,
    $elWrapper: null,
    $options: {},
    $items: {},
    $globalOptions: {},
    $ul: null,
    $header: null,
    $title: null,
    $form: null,
    $footer: null,
    $body: null,
    eventsSuppressed: !1,
    _init: function () {
      var me = this;
      me.suppressEvents(),
        me.$options.id || (me.$options.id = "lobilist-list-" + LIST_COUNTER++);
      var $wrapper = $('<div class="lobilist-wrapper"></div>'),
        $div = $(
          '<div id="' + me.$options.id + '" class="lobilist"></div>'
        ).appendTo($wrapper);
      me.$options.defaultStyle && $div.addClass(me.$options.defaultStyle),
        (me.$el = $div),
        (me.$elWrapper = $wrapper),
        (me.$header = me._createHeader()),
        (me.$title = me._createTitle()),
        (me.$body = me._createBody()),
        (me.$ul = me._createList()),
        me.$options.items && me._createItems(me.$options.items),
        (me.$form = me._createForm()),
        me.$body.append(me.$ul, me.$form),
        (me.$footer = me._createFooter()),
        me.$globalOptions.sortable && me._enableSorting(),
        me.resumeEvents();
    },
    addItem: function (item, errorCallback) {
      var me = this;
      return me._triggerEvent("beforeItemAdd", [me, item]) === !1
        ? me
        : ((item = me._processItemData(item)),
          me.$globalOptions.actions.insert
            ? $.ajax(me.$globalOptions.actions.insert, {
                data: item,
                method: "POST",
              }).done(function (res) {
                res.success
                  ? ((item.id = res.id), me._addItemToList(item))
                  : errorCallback &&
                    "function" == typeof errorCallback &&
                    errorCallback(res);
              })
            : ((item.id = me.$lobiList.getNextId()), me._addItemToList(item)),
          me);
    },
    updateItem: function (item, errorCallback) {
      var me = this;
      return me._triggerEvent("beforeItemUpdate", [me, item]) === !1
        ? me
        : (me.$globalOptions.actions.update
            ? $.ajax(me.$globalOptions.actions.update, {
                data: item,
                method: "POST",
              }).done(function (res) {
                res.success
                  ? me._updateItemInList(item)
                  : errorCallback &&
                    "function" == typeof errorCallback &&
                    errorCallback(res);
              })
            : me._updateItemInList(item),
          me);
    },
    deleteItem: function (item, errorCallback) {
      var me = this;
      return me._triggerEvent("beforeItemDelete", [me, item]) === !1
        ? me
        : me.$globalOptions.actions["delete"]
        ? me
            ._sendAjax(me.$globalOptions.actions["delete"], {
              data: item,
              method: "POST",
            })
            .done(function (res) {
              res.success
                ? me._removeItemFromList(item)
                : errorCallback &&
                  "function" == typeof errorCallback &&
                  errorCallback(res);
            })
        : (me._removeItemFromList(item), me);
    },
    saveOrUpdateItem: function (item, errorCallback) {
      var me = this;
      return (
        item.id
          ? me.updateItem(item, errorCallback)
          : me.addItem(item, errorCallback),
        me
      );
    },
    startTitleEditing: function () {
      var me = this,
        input = me._createInput();
      return (
        me.$title.attr("data-old-title", me.$title.html()),
        input.val(me.$title.html()),
        input.insertAfter(me.$title),
        me.$title.addClass("hide"),
        me.$header.addClass("title-editing"),
        input[0].focus(),
        input[0].select(),
        me
      );
    },
    finishTitleEditing: function () {
      var me = this,
        $input = me.$header.find("input"),
        oldTitle = me.$title.attr("data-old-title");
      return (
        me.$title
          .html($input.val())
          .removeClass("hide")
          .removeAttr("data-old-title"),
        $input.remove(),
        me.$header.removeClass("title-editing"),
        console.log(oldTitle, $input.val()),
        me._triggerEvent("titleChange", [me, oldTitle, $input.val()]),
        me
      );
    },
    cancelTitleEditing: function () {
      var me = this,
        $input = me.$header.find("input");
      return 0 === $input.length
        ? me
        : (me.$title.html(me.$title.attr("data-old-title")).removeClass("hide"),
          $input.remove(),
          me.$header.removeClass("title-editing"),
          me);
    },
    remove: function () {
      var me = this;
      return (
        me.$lobiList.$lists.splice(me.$el.index(), 1),
        me.$elWrapper.remove(),
        me
      );
    },
    editItem: function (id) {
      var me = this,
        $item = me.$lobiList.$el.find("li[data-id=" + id + "]"),
        $form = $item.closest(".lobilist").find(".lobilist-add-todo-form"),
        $footer = $item.closest(".lobilist").find(".lobilist-footer");
      return (
        $form.removeClass("hide"),
        $footer.addClass("hide"),
        ($form[0].id.value = $item.attr("data-id")),
        ($form[0].title.value = $item.find(".lobilist-item-title").html()),
        ($form[0].description.value =
          $item.find(".lobilist-item-description").html() || ""),
        ($form[0].dueDate.value =
          $item.find(".lobilist-item-duedate").html() || ""),
        me
      );
    },
    suppressEvents: function () {
      return (this.eventsSuppressed = !0), this;
    },
    resumeEvents: function () {
      return (this.eventsSuppressed = !1), this;
    },
    _processItemData: function (item) {
      var me = this;
      return $.extend({}, me.$globalOptions.itemOptions, item);
    },
    _createHeader: function () {
      var me = this,
        $header = $("<div>", { class: "lobilist-header" }),
        $actions = $("<div>", { class: "lobilist-actions" }).appendTo($header);
      return (
        me.$options.controls &&
          me.$options.controls.length > 0 &&
          (me.$options.controls.indexOf("styleChange") > -1 &&
            $actions.append(me._createDropdownForStyleChange()),
          me.$options.controls.indexOf("edit") > -1 &&
            ($actions.append(me._createEditTitleButton()),
            $actions.append(me._createFinishTitleEditing()),
            $actions.append(me._createCancelTitleEditing())),
          me.$options.controls.indexOf("add") > -1 &&
            $actions.append(me._createAddNewButton()),
          me.$options.controls.indexOf("remove") > -1 &&
            $actions.append(me._createCloseButton())),
        me.$el.append($header),
        $header
      );
    },
    _createTitle: function () {
      var me = this,
        $title = $("<div>", {
          class: "lobilist-title",
          html: me.$options.title,
        }).appendTo(me.$header);
      return (
        me.$options.controls &&
          me.$options.controls.indexOf("edit") > -1 &&
          $title.on("dblclick", function () {
            me.startTitleEditing();
          }),
        $title
      );
    },
    _createBody: function () {
      var me = this;
      return $("<div>", { class: "lobilist-body" }).appendTo(me.$el);
    },
    _createForm: function () {
      var me = this,
        $form = $("<form>", { class: "lobilist-add-todo-form hide" });
      $('<input type="hidden" name="id">').appendTo($form),
        $('<div class="form-group">')
          .append(
            $("<input>", {
              type: "text",
              name: "title",
              class: "form-control",
              placeholder: "TODO title",
            })
          )
          .appendTo($form),
        $('<div class="form-group">')
          .append(
            $("<textarea>", {
              rows: "2",
              name: "description",
              class: "form-control",
              placeholder: "TODO description",
            })
          )
          .appendTo($form),
        $('<div class="form-group">')
          .append(
            $("<input>", {
              type: "text",
              name: "dueDate",
              class: "form-control",
              placeholder: "Due Date",
            })
          )
          .appendTo($form);
      var $ft = $('<div class="lobilist-form-footer">');
      return (
        $("<button>", {
          class: "btn btn-primary btn-sm btn-add-todo",
          html: "Add",
        }).appendTo($ft),
        $("<button>", {
          type: "button",
          class: "btn btn-default btn-sm btn-discard-todo",
          html: '<i class="glyphicon glyphicon-remove-circle"></i>',
        })
          .click(function () {
            $form.addClass("hide"), me.$footer.removeClass("hide");
          })
          .appendTo($ft),
        $ft.appendTo($form),
        me._formHandler($form),
        me.$el.append($form),
        $form
      );
    },
    _formHandler: function ($form) {
      var me = this;
      $form.on("submit", function (ev) {
        ev.preventDefault(), me._submitForm();
      });
    },
    _submitForm: function () {
      var me = this;
      return me.$form[0].title.value
        ? (me.saveOrUpdateItem({
            id: me.$form[0].id.value,
            title: me.$form[0].title.value,
            description: me.$form[0].description.value,
            dueDate: me.$form[0].dueDate.value,
          }),
          me.$form.addClass("hide"),
          void me.$footer.removeClass("hide"))
        : void me._showFormError("title", "Title can not be empty");
    },
    _createFooter: function () {
      var me = this,
        $footer = $("<div>", { class: "lobilist-footer" });
      return (
        $("<button>", {
          type: "button",
          class: "btn-link btn-show-form",
          html: "Add new",
        })
          .click(function () {
            me._resetForm(),
              me.$form.removeClass("hide"),
              $footer.addClass("hide");
          })
          .appendTo($footer),
        me.$el.append($footer),
        $footer
      );
    },
    _createList: function () {
      var me = this,
        $list = $("<ul>", { class: "lobilist-items" });
      return me.$el.append($list), $list;
    },
    _createItems: function (items) {
      for (var me = this, i = 0; i < items.length; i++) me._addItem(items[i]);
    },
    _addItem: function (item) {
      var me = this;
      item.id || (item.id = me.$lobiList.getNextId()),
        me._triggerEvent("beforeItemAdd", [me, item]) !== !1 &&
          ((item = me._processItemData(item)), me._addItemToList(item));
    },
    _createCheckbox: function () {
      var me = this,
        $item = $("<input>", { type: "checkbox" });
      return (
        $item.change(function () {
          me._onCheckboxChange(this);
        }),
        $("<label>", { class: "checkbox-inline lobilist-check" }).append($item)
      );
    },
    _onCheckboxChange: function (checkbox) {
      var me = this,
        $this = $(checkbox);
      $this.prop("checked")
        ? me._triggerEvent("afterMarkAsDone", [me, $this])
        : me._triggerEvent("afterMarkAsUndone", [me, $this]),
        $this.closest(".lobilist-item").toggleClass("item-done");
    },
    _createDropdownForStyleChange: function () {
      for (
        var me = this,
          $dropdown = $("<div>", { class: "dropdown" }).append(
            $("<button>", {
              type: "button",
              "data-toggle": "dropdown",
              class: "btn btn-default btn-xs",
              html: '<i class="glyphicon glyphicon-th"></i>',
            })
          ),
          $menu = $("<div>", {
            class: "dropdown-menu dropdown-menu-right",
          }).appendTo($dropdown),
          i = 0;
        i < me.$globalOptions.listStyles.length;
        i++
      ) {
        var st = me.$globalOptions.listStyles[i];
        $('<div class="' + st + '"></div>')
          .on("mousedown", function (ev) {
            ev.stopPropagation();
          })
          .click(function () {
            for (
              var classes = me.$el[0].className.split(" "),
                oldClass = null,
                i = 0;
              i < classes.length;
              i++
            )
              me.$globalOptions.listStyles.indexOf(classes[i]) > -1 &&
                (oldClass = classes[i]);
            me.$el
              .removeClass(me.$globalOptions.listStyles.join(" "))
              .addClass(this.className),
              me._triggerEvent("styleChange", [me, oldClass, this.className]);
          })
          .appendTo($menu);
      }
      return $dropdown;
    },
    _createEditTitleButton: function () {
      var me = this,
        $btn = $("<button>", {
          class: "btn btn-default btn-xs",
          html: '<i class="glyphicon glyphicon-edit"></i>',
        });
      return (
        $btn.click(function () {
          me.startTitleEditing();
        }),
        $btn
      );
    },
    _createAddNewButton: function () {
      var me = this,
        $btn = $("<button>", {
          class: "btn btn-default btn-xs",
          html: '<i class="glyphicon glyphicon-plus"></i>',
        });
      return (
        $btn.click(function () {
          var list = me.$lobiList.addList();
          list.startTitleEditing();
        }),
        $btn
      );
    },
    _createCloseButton: function () {
      var me = this,
        $btn = $("<button>", {
          class: "btn btn-default btn-xs",
          html: '<i class="glyphicon glyphicon-remove"></i>',
        });
      return $btn.click(me._onRemoveListClick), $btn;
    },
    _onRemoveListClick: function () {
      var me = this;
      return (
        me._triggerEvent("beforeListRemove", [me]),
        me.remove(),
        me._triggerEvent("afterListRemove", [me]),
        me
      );
    },
    _createFinishTitleEditing: function () {
      var me = this,
        $btn = $("<button>", {
          class: "btn btn-default btn-xs btn-finish-title-editing",
          html: '<i class="glyphicon glyphicon-ok-circle"></i>',
        });
      return (
        $btn.click(function () {
          me.finishTitleEditing();
        }),
        $btn
      );
    },
    _createCancelTitleEditing: function () {
      var me = this,
        $btn = $("<button>", {
          class: "btn btn-default btn-xs btn-cancel-title-editing",
          html: '<i class="glyphicon glyphicon-remove-circle"></i>',
        });
      return (
        $btn.click(function () {
          me.cancelTitleEditing();
        }),
        $btn
      );
    },
    _createInput: function () {
      var me = this,
        input = $('<input type="text" class="form-control">');
      return (
        input.on("keyup", function (ev) {
          13 === ev.which && me.finishTitleEditing();
        }),
        input
      );
    },
    _showFormError: function (field, error) {
      var $fGroup = this.$form
        .find('[name="' + field + '"]')
        .closest(".form-group")
        .addClass("has-error");
      $fGroup.find(".help-block").remove(),
        $fGroup.append($('<span class="help-block">' + error + "</span>"));
    },
    _resetForm: function () {
      var me = this;
      me.$form[0].reset(),
        (me.$form[0].id.value = ""),
        me.$form
          .find(".form-group")
          .removeClass("has-error")
          .find(".help-block")
          .remove();
    },
    _enableSorting: function () {
      var me = this;
      me.$el.find(".lobilist-items").sortable({
        connectWith: ".lobilist .lobilist-items",
        items: ".lobilist-item",
        handle: ".drag-handler",
        cursor: "move",
        placeholder: "lobilist-item-placeholder",
        forcePlaceholderSize: !0,
        opacity: 0.9,
        revert: 70,
        update: function (event, ui) {
          me._triggerEvent("afterItemReorder", [me, ui.item]);
        },
      });
    },
    _addItemToList: function (item) {
      var me = this,
        $li = $("<li>", { "data-id": item.id, class: "lobilist-item" });
      return (
        $li.append(
          $("<div>", { class: "lobilist-item-title", html: item.title })
        ),
        item.description &&
          $li.append(
            $("<div>", {
              class: "lobilist-item-description",
              html: item.description,
            })
          ),
        item.dueDate &&
          $li.append(
            $("<div>", { class: "lobilist-item-duedate", html: item.dueDate })
          ),
        ($li = me._addItemControls($li)),
        item.done &&
          ($li.find("input[type=checkbox]").prop("checked", !0),
          $li.addClass("item-done")),
        $li.data("lobiListItem", item),
        me.$ul.append($li),
        (me.$items[item.id] = item),
        me._triggerEvent("afterItemAdd", [me, item]),
        $li
      );
    },
    _addItemControls: function ($li) {
      var me = this;
      me.$options.useCheckboxes && $li.append(me._createCheckbox());
      var $itemControlsDiv = $("<div>", { class: "todo-actions" }).appendTo(
        $li
      );
      return (
        me.$options.enableTodoEdit &&
          $itemControlsDiv.append(
            $("<div>", {
              class: "edit-todo todo-action",
              html: '<i class="glyphicon glyphicon-pencil"></i>',
            }).click(function () {
              me.editItem($(this).closest("li").data("id"));
            })
          ),
        me.$options.enableTodoRemove &&
          $itemControlsDiv.append(
            $("<div>", {
              class: "delete-todo todo-action",
              html: '<i class="glyphicon glyphicon-remove"></i>',
            }).click(function () {
              me._onDeleteItemClick($(this).closest("li").data("lobiListItem"));
            })
          ),
        $li.append($("<div>", { class: "drag-handler" })),
        $li
      );
    },
    _onDeleteItemClick: function (item) {
      this.deleteItem(item);
    },
    _updateItemInList: function (item) {
      var me = this,
        $li = me.$lobiList.$el.find('li[data-id="' + item.id + '"]');
      $li.find("input[type=checkbox]").prop("checked", item.done),
        $li.find(".lobilist-item-title").html(item.title),
        $li.find(".lobilist-item-description").remove(),
        $li.find(".lobilist-item-duedate").remove(),
        item.description &&
          $li.append(
            '<div class="lobilist-item-description">' +
              item.description +
              "</div>"
          ),
        item.dueDate &&
          $li.append(
            '<div class="lobilist-item-duedate">' + item.dueDate + "</div>"
          ),
        $li.data("lobiListItem", item),
        $.extend(me.$items[item.id], item),
        me._triggerEvent("afterItemUpdate", [me, item]);
    },
    _triggerEvent: function (type, data) {
      var me = this;
      if (!me.eventsSuppressed)
        return me.$options[type] && "function" == typeof me.$options[type]
          ? me.$options[type].apply(me, data)
          : me.$el.trigger(type, data);
    },
    _removeItemFromList: function (item) {
      var me = this;
      me.$lobiList.$el.find("li[data-id=" + item.id + "]").remove(),
        me._triggerEvent("afterItemDelete", [me, item]);
    },
    _sendAjax: function (url, params) {
      var me = this;
      return $.ajax(url, me._beforeAjaxSent(params));
    },
    _beforeAjaxSent: function (params) {
      var me = this,
        eventParams = me._triggerEvent("beforeAjaxSent", [me, params]);
      return $.extend({}, params, eventParams || {});
    },
  };
  var LobiList = function ($el, options) {
    (this.$el = $el), this.init(options);
  };
  (LobiList.prototype = {
    $el: null,
    $lists: [],
    $options: {},
    _nextId: 1,
    eventsSuppressed: !1,
    init: function (options) {
      var me = this;
      me.suppressEvents(),
        (me.$options = this._processInput(options)),
        me.$el.addClass("lobilists"),
        me.$options.onSingleLine && me.$el.addClass("single-line"),
        me._createLists(),
        me._handleSortable(),
        me._triggerEvent("init", [me]),
        me.resumeEvents();
    },
    _processInput: function (options) {
      return (
        (options = $.extend({}, $.fn.lobiList.DEFAULT_OPTIONS, options)),
        options.actions.load &&
          $.ajax(options.actions.load, { async: !1 }).done(function (res) {
            options.lists = res.lists;
          }),
        options
      );
    },
    _createLists: function () {
      for (var me = this, i = 0; i < me.$options.lists.length; i++)
        me.addList(me.$options.lists[i]);
      return me;
    },
    _handleSortable: function () {
      var me = this;
      return (
        me.$options.sortable
          ? me.$el.sortable({
              items: ".lobilist-wrapper",
              handle: ".lobilist-header",
              cursor: "move",
              placeholder: "lobilist-placeholder",
              forcePlaceholderSize: !0,
              opacity: 0.9,
              revert: 70,
              update: function (event, ui) {
                me._triggerEvent("afterListReorder", [
                  me,
                  ui.item.find(".lobilist").data("lobiList"),
                ]);
              },
            })
          : me.$el.addClass("no-sortable"),
        me
      );
    },
    addList: function (list) {
      var me = this;
      return (
        list instanceof List ||
          (list = new List(me, me._processListOptions(list))),
        me._triggerEvent("beforeListAdd", [me, list]) !== !1 &&
          (me.$lists.push(list),
          me.$el.append(list.$elWrapper),
          list.$el.data("lobiList", list),
          me._triggerEvent("afterListAdd", [me, list])),
        list
      );
    },
    destroy: function () {
      var me = this;
      if (me._triggerEvent("beforeDestroy", [me]) !== !1) {
        for (var i = 0; i < me.$lists.length; i++) me.$lists[i].remove();
        me.$options.sortable && me.$el.sortable("destroy"),
          me.$el.removeClass("lobilists"),
          me.$options.onSingleLine && me.$el.removeClass("single-line"),
          me.$el.removeData("lobiList"),
          me._triggerEvent("afterDestroy", [me]);
      }
      return me;
    },
    getNextId: function () {
      return this._nextId++;
    },
    _processListOptions: function (listOptions) {
      var me = this;
      listOptions = $.extend({}, me.$options.listsOptions, listOptions);
      for (var i in me.$options)
        me.$options.hasOwnProperty(i) &&
          void 0 === listOptions[i] &&
          (listOptions[i] = me.$options[i]);
      return listOptions;
    },
    suppressEvents: function () {
      return (this.eventsSuppressed = !0), this;
    },
    resumeEvents: function () {
      return (this.eventsSuppressed = !1), this;
    },
    _triggerEvent: function (type, data) {
      var me = this;
      if (!me.eventsSuppressed)
        return me.$options[type] && "function" == typeof me.$options[type]
          ? me.$options[type].apply(me, data)
          : me.$el.trigger(type, data);
    },
  }),
    ($.fn.lobiList = function (option) {
      var ret,
        args = arguments;
      return this.each(function () {
        var $this = $(this),
          data = $this.data("lobiList"),
          options = "object" == typeof option && option;
        data || $this.data("lobiList", (data = new LobiList($this, options))),
          "string" == typeof option &&
            ((args = Array.prototype.slice.call(args, 1)),
            (ret = data[option].apply(data, args)));
      });
    }),
    ($.fn.lobiList.DEFAULT_OPTIONS = {
      listStyles: [
        "lobilist-default",
        "lobilist-danger",
        "lobilist-success",
        "lobilist-warning",
        "lobilist-info",
        "lobilist-primary",
      ],
      listsOptions: { id: !1, title: "", items: [] },
      itemOptions: {
        id: !1,
        title: "",
        description: "",
        dueDate: "",
        done: !1,
      },
      lists: [],
      actions: { load: "", update: "", insert: "", delete: "" },
      useCheckboxes: !0,
      enableTodoRemove: !0,
      enableTodoEdit: !0,
      sortable: !0,
      controls: ["edit", "add", "remove", "styleChange"],
      defaultStyle: "lobilist-default",
      onSingleLine: !0,
      init: null,
      beforeDestroy: null,
      afterDestroy: null,
      beforeListAdd: null,
      afterListAdd: null,
      beforeListRemove: null,
      afterListRemove: null,
      beforeItemAdd: null,
      afterItemAdd: null,
      beforeItemUpdate: null,
      afterItemUpdate: null,
      beforeItemDelete: null,
      afterItemDelete: null,
      afterListReorder: null,
      afterItemReorder: null,
      afterMarkAsDone: null,
      afterMarkAsUndone: null,
      beforeAjaxSent: null,
      styleChange: null,
      titleChange: null,
    });
});
