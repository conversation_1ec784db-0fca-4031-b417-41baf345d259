services:
  raycoapp:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: raycoapp
    ports:
      - "${APP_PORT}:${APP_PORT}"
    volumes:
      - ./src:/app/src
      - ./public:/app/public
      - ./node_modules:/app/node_modules
      - ./package.json:/app/package.json
      - ./package-lock.json:/app/package-lock.json
      - ./logs:/app/logs
    env_file:
      - .env
    working_dir: /app

    networks:
      - backend
      
    command: ["npm", "run", "start"]


networks:
  backend:
    external: true