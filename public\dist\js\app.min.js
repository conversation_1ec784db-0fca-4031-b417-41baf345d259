function msg_receive(a) {
    var e = new Date;
    return "<li class='msg_receive'><div class='chat-content'><div class='box bg-light-info'>" + a + "</div></div><div class='chat-time'>" + e.getHours() + ":" + e.getMinutes() + "</div></li>"
}
function msg_sent(a) {
    var e = new Date;
    return "<li class='odd msg_sent'><div class='chat-content'><div class='box bg-light-info'>" + a + "</div><br></div><div class='chat-time'>" + e.getHours() + ":" + e.getMinutes() + "</div></li>"
}
$.fn.AdminSettings = function(t) {
    var i = this.attr("id")
      , t = $.extend({}, {
        Theme: !0,
        Layout: "vertical",
        LogoBg: "skin1",
        NavbarBg: "skin6",
        SidebarType: "full",
        SidebarColor: "skin1",
        SidebarPosition: !1,
        HeaderPosition: !1,
        BoxedLayout: !1
    }, t)
      , a = {
        AdminSettingsInit: function() {
            a.ManageTheme(),
            a.ManageThemeLayout(),
            a.ManageThemeBackground(),
            a.ManageSidebarType(),
            a.ManageSidebarColor(),
            a.ManageSidebarPosition(),
            a.ManageBoxedLayout()
        },
        ManageTheme: function() {
            var a = t.Theme;
            "vertical" === t.Layout && (1 == a ? ($("body").attr("data-theme", "dark"),
            $("#theme-view").prop("checked", !0)) : ($("#" + i).attr("data-theme", "light"),
            $("body").prop("checked", !1)))
        },
        ManageThemeLayout: function() {
            switch (t.Layout) {
            case "horizontal":
                $("#" + i).attr("data-layout", "horizontal");
                function a() {
                    (0 < window.innerWidth ? window.innerWidth : this.screen.width) < 768 ? $(".scroll-sidebar").perfectScrollbar({}) : $(".scroll-sidebar").perfectScrollbar("destroy")
                }
                $(window).ready(a),
                $(window).on("resize", a);
                break;
            case "vertical":
                $("#" + i).attr("data-layout", "vertical"),
                $(".scroll-sidebar").perfectScrollbar({})
            }
        },
        ManageThemeBackground: function() {
            var a;
            null != (a = t.LogoBg) && "" != a ? $("#" + i + " .topbar .top-navbar .navbar-header").attr("data-logobg", a) : $("#" + i + " .topbar .top-navbar .navbar-header").attr("data-logobg", "skin1"),
            null != (a = t.NavbarBg) && "" != a ? ($("#" + i + " .topbar .navbar-collapse").attr("data-navbarbg", a),
            $("#" + i + " .topbar").attr("data-navbarbg", a),
            $("#" + i).attr("data-navbarbg", a)) : ($("#" + i + " .topbar .navbar-collapse").attr("data-navbarbg", "skin1"),
            $("#" + i + " .topbar").attr("data-navbarbg", "skin1"),
            $("#" + i).attr("data-navbarbg", "skin1"))
        },
        ManageSidebarType: function() {
            switch (t.SidebarType) {
            case "full":
                $("#" + i).attr("data-sidebartype", "full");
                var a = function() {
                    (0 < window.innerWidth ? window.innerWidth : this.screen.width) < 1170 ? ($("#main-wrapper").attr("data-sidebartype", "mini-sidebar"),
                    $("#main-wrapper").addClass("mini-sidebar")) : ($("#main-wrapper").attr("data-sidebartype", "full"),
                    $("#main-wrapper").removeClass("mini-sidebar"))
                };
                $(window).ready(a),
                $(window).on("resize", a),
                $(".sidebartoggler").on("click", function() {
                    $("#main-wrapper").toggleClass("mini-sidebar"),
                    $("#main-wrapper").hasClass("mini-sidebar") ? ($(".sidebartoggler").prop("checked", !0),
                    $("#main-wrapper").attr("data-sidebartype", "mini-sidebar")) : ($(".sidebartoggler").prop("checked", !1),
                    $("#main-wrapper").attr("data-sidebartype", "full"))
                });
                break;
            case "mini-sidebar":
                $("#" + i).attr("data-sidebartype", "mini-sidebar"),
                $(".sidebartoggler").on("click", function() {
                    $("#main-wrapper").toggleClass("mini-sidebar"),
                    $("#main-wrapper").hasClass("mini-sidebar") ? ($(".sidebartoggler").prop("checked", !0),
                    $("#main-wrapper").attr("data-sidebartype", "full")) : ($(".sidebartoggler").prop("checked", !1),
                    $("#main-wrapper").attr("data-sidebartype", "mini-sidebar"))
                });
                break;
            case "iconbar":
                $("#" + i).attr("data-sidebartype", "iconbar");
                a = function() {
                    (0 < window.innerWidth ? window.innerWidth : this.screen.width) < 1170 ? ($("#main-wrapper").attr("data-sidebartype", "mini-sidebar"),
                    $("#main-wrapper").addClass("mini-sidebar")) : ($("#main-wrapper").attr("data-sidebartype", "iconbar"),
                    $("#main-wrapper").removeClass("mini-sidebar"))
                }
                ;
                $(window).ready(a),
                $(window).on("resize", a),
                $(".sidebartoggler").on("click", function() {
                    $("#main-wrapper").toggleClass("mini-sidebar"),
                    $("#main-wrapper").hasClass("mini-sidebar") ? ($(".sidebartoggler").prop("checked", !0),
                    $("#main-wrapper").attr("data-sidebartype", "mini-sidebar")) : ($(".sidebartoggler").prop("checked", !1),
                    $("#main-wrapper").attr("data-sidebartype", "iconbar"))
                });
                break;
            case "overlay":
                $("#" + i).attr("data-sidebartype", "overlay");
                a = function() {
                    (0 < window.innerWidth ? window.innerWidth : this.screen.width) < 767 ? ($("#main-wrapper").attr("data-sidebartype", "mini-sidebar"),
                    $("#main-wrapper").addClass("mini-sidebar")) : ($("#main-wrapper").attr("data-sidebartype", "overlay"),
                    $("#main-wrapper").removeClass("mini-sidebar"))
                }
                ;
                $(window).ready(a),
                $(window).on("resize", a),
                $(".sidebartoggler").on("click", function() {
                    $("#main-wrapper").toggleClass("show-sidebar"),
                    $("#main-wrapper").hasClass("show-sidebar")
                })
            }
        },
        ManageSidebarColor: function() {
            var a;
            null != (a = t.SidebarColor) && "" != a ? $("#" + i + " .left-sidebar").attr("data-sidebarbg", a) : $("#" + i + " .left-sidebar").attr("data-sidebarbg", "skin1")
        },
        ManageSidebarPosition: function() {
            var a = t.SidebarPosition
              , e = t.HeaderPosition;
            switch (t.Layout) {
            case "vertical":
            case "horizontal":
                1 == a ? ($("#" + i).attr("data-sidebar-position", "fixed"),
                $("#sidebar-position").prop("checked", !0)) : ($("#" + i).attr("data-sidebar-position", "absolute"),
                $("#sidebar-position").prop("checked", !1)),
                1 == e ? ($("#" + i).attr("data-header-position", "fixed"),
                $("#header-position").prop("checked", !0)) : ($("#" + i).attr("data-header-position", "relative"),
                $("#header-position").prop("checked", !1))
            }
        },
        ManageBoxedLayout: function() {
            var a = t.BoxedLayout;
            switch (t.Layout) {
            case "vertical":
            case "horizontal":
                1 == a ? ($("#" + i).attr("data-boxed-layout", "boxed"),
                $("#boxed-layout").prop("checked", !0)) : ($("#" + i).attr("data-boxed-layout", "full"),
                $("#boxed-layout").prop("checked", !1))
            }
        }
    };
    a.AdminSettingsInit()
}
,
$(function() {
    $("#chat");
    $("#chat .message-center a").on("click", function() {
        var a, e, t = $(this).find(".mail-contnet h5").text(), i = $(this).find(".user-img img").attr("src"), r = $(this).attr("data-user-id"), s = $(this).find(".profile-status").attr("data-status");
        $(this).hasClass("active") ? ($(this).toggleClass("active"),
        $(".chat-windows #user-chat" + r).hide()) : ($(this).toggleClass("active"),
        $(".chat-windows #user-chat" + r).length ? $(".chat-windows #user-chat" + r).removeClass("mini-chat").show() : (a = msg_receive("I watched the storm, so beautiful yet terrific."),
        e = (e = (e = "<div class='user-chat' id='user-chat" + r + "' data-user-id='" + r + "'>") + "<div class='chat-head'><img src='" + i + "' data-user-id='" + r + "'><span class='status " + s + "'></span><span class='name'>" + t + "</span><span class='opts'><i class='ti-close closeit' data-user-id='" + r + "'></i><i class='ti-minus mini-chat' data-user-id='" + r + "'></i></span></div><div class='chat-body'><ul class='chat-list'>" + (a += msg_sent("That is very deep indeed!")) + "</ul></div>") + "<div class='chat-footer'><input type='text' data-user-id='" + r + "' placeholder='Type & Enter' class='form-control'></div></div>",
        $(".chat-windows").append(e)))
    }),
    $(document).on("click", ".chat-windows .user-chat .chat-head .closeit", function(a) {
        var e = $(this).attr("data-user-id");
        $(".chat-windows #user-chat" + e).hide(),
        $("#chat .message-center .user-info#chat_user_" + e).removeClass("active")
    }),
    $(document).on("click", ".chat-windows .user-chat .chat-head img, .chat-windows .user-chat .chat-head .mini-chat", function(a) {
        var e = $(this).attr("data-user-id");
        $(".chat-windows #user-chat" + e).hasClass("mini-chat") ? $(".chat-windows #user-chat" + e).removeClass("mini-chat") : $(".chat-windows #user-chat" + e).addClass("mini-chat")
    }),
    $(document).on("keypress", ".chat-windows .user-chat .chat-footer input", function(a) {
        var e;
        13 == a.keyCode && (e = $(this).attr("data-user-id"),
        a = msg_sent(a = $(this).val()),
        $(".chat-windows #user-chat" + e + " .chat-body .chat-list").append(a),
        $(this).val(""),
        $(this).focus()),
        $(".chat-windows #user-chat" + e + " .chat-body").perfectScrollbar({
            suppressScrollX: !0
        })
    }),
    $(".page-wrapper").on("click", function(a) {
        $(".chat-windows").addClass("hide-chat"),
        $(".chat-windows").removeClass("show-chat")
    }),
    $(".service-panel-toggle").on("click", function(a) {
        $(".chat-windows").addClass("show-chat"),
        $(".chat-windows").removeClass("hide-chat")
    })
});
