Funcionabm.FUNCION_ABRIR_MODAL_GUARDAR = "iniciargrillaabmrasociada"
Funcionabm.FUNCION_ABRIR_MODAL_EDIT = "iniciargrillaabmrasociada";
Funcionabm.FUNCION_ANTES_GUARDAR_EDITAR = "establecerValoresPorDefecto";


$(document).ready(function () {
    cargarddformularios();
    //cargartiposcomprobanmtes();    
});

// function cargartiposcomprobanmtes() {    
//     let dts_from = clickFunciones.callWebservice("PROC_GET_TIPOS_COMPROBANTES", {});
//     cargardtsselect2("TIPO_COMPROBANTE", "ID_TIPO_COMPROBANTE","DESCRIPCION", dts_from, "modal_USUARIOS");
// }

function cargarddformularios() {
    let selectOptions = [{ id: "", text: "Seleccionar..." }];
    let dts_from = clickFunciones.callWebservice("PROC_GET_ROLES_ACTIVOS", {});
    dts_from.forEach((data) => {
        selectOptions.push({ id: data.ID_ROL, text: data.DESCRIPCION });
    });

    const $select = $('#ROL');
    $select.select2({
        dropdownParent: $('#modal_USUARIOS'),
        data: selectOptions,
        dropdownAutoWidth: true,
        width: '100%'
    });
}

function cargardtsselect2(idSelect, valor, texto, datos, modal = ""){
    try {
        let selectOptions = [{ id: "", text: "Seleccionar..." }];
        datos.forEach((data) => {
            selectOptions.push({ id: data[valor], text: data[texto] });
        });
        const $select = $('#'+ idSelect);
        datosSelect2 = {};
        if(modal)
            datosSelect2.dropdownParent = "#"+ modal;
        datosSelect2.data = selectOptions;
        datosSelect2.dropdownAutoWidth = true;
        datosSelect2.width = '100%';
        $select.select2(datosSelect2);
    } catch (error) {
        console.log(error);
    }
}

function iniciargrillaabmrasociada() {
    try {
        Funcionabm.iniciagrillatablaasociada("USUARIOS_ROLES");
        //Funcionabm.iniciagrillatablaasociada("USUARIOS_TIPOS_COMPROB");
    } catch (error) {
        console.log(error);
    }
}

function establecerValoresPorDefecto(tabla = "USUARIOS") {
    try {
        // Establecer valor por defecto para CLAVE_ACCESO
        const claveAccesoField = $(`.${tabla} [name="CLAVE_ACCESO"]`);
        if (claveAccesoField.length > 0 && !claveAccesoField.val()) {
            claveAccesoField.val("222222");
        }

        // Establecer valor por defecto para ID_SUCURSAL
        const sucursalField = $(`.${tabla} [name="ID_SUCURSAL"]`);
        if (sucursalField.length > 0 && !sucursalField.val()) {
            sucursalField.val("1");
        }

        return true; // Permitir continuar con el guardado
    } catch (error) {
        console.error("Error al establecer valores por defecto:", error);
        return true; // Permitir continuar aunque haya error
    }
}
