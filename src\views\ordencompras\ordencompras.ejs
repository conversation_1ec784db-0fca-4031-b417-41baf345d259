<style type="text/css">
    .select2-container--default .select2-selection--multiple {
        /*line-height: 50px !important;*/
        height: 59px !important;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        display: block !important;
    }

    #IMPORTE_TOTAL {
        border-width: 2px;
        border-style: solid;
        border-color: green;
        background: #0080001a;
        height: 45px;
    }

    .camposuma {
        width: 100%;
        background: white !important;
        color: black;
        border-block: black;
        border: 1px solid;
    }
</style>

<div class="row">
    <div class="col-lg-12 col-md-12">
        <div class="card">
            <div class="border-bottom title-part-padding" style="display: none;">
                <h4 id="nomformtitle" class="card-title mb-0"></h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="accordion mb-2" id="accordionFlushExample">
                        <div class="accordion-item">
                            <h2 class="accordion-header mb-2" id="flush-headingOne">
                                <button class="accordion-button custom-info" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#flush-collapseOne" aria-expanded="false"
                                    aria-controls="flush-collapseOne">
                                    Detalle Comprobante
                                </button>
                            </h2>
                            <div id="flush-collapseOne" class="accordion-collapse collapse show"
                                aria-labelledby="flush-headingOne" data-bs-parent="#accordionFlushExample">
                                <div class="col-md-12">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-floating mb-2">
                                                <select id="PERFIL_COMPROBANTE" class="form-select mr-sm-2" required>
                                                </select>
                                                <label for="PERFIL_COMPROBANTE">Perfil Compronate</label>
                                                <small id="PERFIL_COMPROBANTE_alert" class="invalid-feedback">
                                                    Favor agregar el Perfil
                                                </small>
                                            </div>
                                        </div>
                                        <div id="comprobante-factura" class="col-md-9">
                                            <%- include('../config_talonario/config_talonario.ejs') %>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-floating mb-2">
                                                <input id="FECHA" type="date" class="form-control" required>
                                                <label for="FECHA">Fecha</label>
                                                <sd-feemall id="FECHA" class="invalid-feedback">Favor agregar
                                                    Fecha</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-floating mb-2">
                                                <select id="ID_CONDICION_COMPRA" class="form-select mr-sm-2"
                                                    required></select>
                                                <label for="ID_CONDICION_COMPRA">Cond. Compra</label>
                                                <small id="ID_CONDICION_COMPRA_alert" class="invalid-feedback">
                                                    Favor agregar el Perfil
                                                </small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-floating mb-2">
                                                <select id="ID_DEPOSITO" class="form-select mr-sm-2" required></select>
                                                <label for="ID_DEPOSITO">Deposito Ingreso</label>
                                                <small id="ID_DEPOSITO_alert" class="invalid-feedback">
                                                    Favor agregar el Perfil
                                                </small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-floating mb-2">
                                                <input id="CONCEPTO" type="text" class="form-control"
                                                    placeholder="Enter Name here" required>
                                                <label for="CONCEPTO">Concepto</label>
                                                <sd-feemall id="CONCEPTO_FONDO" class="invalid-feedback">Favor agregar
                                                    el Precio</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-floating mb-2">
                                                <select id="ID_PROVEEDOR" class="w-100 form-control" multiple="multiple"
                                                    data-placeholder="Escriba la Razon Social o RUC"
                                                    style="width: 100%; height: 36px"></select>
                                                <!-- <label for="ID_PRODUCTO">Producto</label> -->
                                                <sd-feemall id="PROVEEDOR_" class="invalid-feedback">Favor agregar la
                                                    Nro. Documento</small>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-floating mb-2">
                                                <input id="NRO_DOCUMENTO" type="text" class="form-control"
                                                    placeholder="Enter Name here" required disabled>
                                                <label for="NRO_DOCUMENTO">RUC</label>
                                                <sd-feemall id="NRO_DOCUMENTO" class="invalid-feedback">Favor
                                                    agregar la Nro. Documento</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-floating mb-2">
                                    <select id="ID_PRODUCTO" class="w-100 form-control" multiple="multiple"
                                        data-placeholder="Escriba Producto" style="width: 100%; height: 36px"></select>
                                    <!-- <label for="ID_PRODUCTO">Producto</label> -->
                                    <sd-feemall id="ID_PRODUCTO_" class="invalid-feedback">Favor agregar la Nro.
                                        Documento</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating mb-2">
                                    <select id="ID_UNIDAD_PRODUCTO" class="form-select mr-sm-2" required></select>
                                    <label for="ID_UNIDAD_PRODUCTO ">Unidad de Medida</label>
                                    <small id="ID_UNIDAD_PRODUCTO " class="invalid-feedback">
                                        Favor agregar la unidad de medida
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-floating mb-2">
                                    <input id="CANTIDAD" type="text" class="form-control numero"
                                        placeholder="Enter Name here" required>
                                    <label for="CANTIDAD">Cantidad</label>
                                    <sd-feemall id="CANTIDAD_" class="invalid-feedback">Favor agregar la
                                        Cantidad</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-floating mb-2">
                                    <input id="PRECIO" type="text" class="form-control numero"
                                        placeholder="Enter Name here" required>
                                    <label for="PRECIO">Precio Compra</label>
                                    <sd-feemall id="PRECIO" class="invalid-feedback">Favor agregar el Precio</small>
                                </div>
                            </div>
                            <div class="col-md-2" style="display: none;">
                                <div class="form-floating mb-2">
                                    <input id="DESCUENTO" type="text" class="form-control numero"
                                        placeholder="Enter Name here" required>
                                    <label for="DESCUENTO">Descuento</label>
                                </div>
                            </div>
                            <div class="col-md-2" style="display: none;">
                                <div class="form-floating mb-2">
                                    <select id="LOTE" class="form-select mr-sm-2" required></select>
                                    <label for="LOTE">Lote</label>
                                    <small id="LOTE" class="invalid-feedback"> Favor agregar el Lote </small>
                                </div>
                            </div>
                            <div class="col-md-2" style="display: none;">
                                <div class="form-floating mb-3">
                                    <input name="FECHA_VENCIMIENTO" type="date" class="form-control">
                                    <label for="FECHA_VENCIMIENTO">Fecha Vencimiento</label>
                                    <sd-feemall id="FECHA_VENCIMIENTO" class="invalid-feedback">Favor agregar
                                        Fecha de Vencimiento</small>
                                </div>
                            </div>
                            <div class="col-md-12 text-end">
                                <button class="btn btn-outline-success" type="button" onclick="agregarcolumnasgrilla()">
                                    Agregar <i class="mdi mdi-plus"></i>
                                </button>
                            </div>
                            <div class="col-lg-12 col-md-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-bordered display"
                                                id="GRILLA_COMP_COMPRA" style="width: 100%;">
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-10 text-end">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-12 control-label col-form-label"
                                                style="font-weight: bold;">Importe
                                                Total</label>
                                            <input id="IMPORTE_TOTAL" type="text"
                                                class="form-control text-uppercase numero" aria-describedby="maxval"
                                                disabled style="font-size: xx-large;">
                                        </div>
                                    </div>
                                    <div class="col-lg-12 col-md-12">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table>
                                                        <thead>
                                                            <tr>
                                                                <th style="text-align: left;"> </th>
                                                                <th>Neto</th>
                                                                <th>Exento</th>
                                                                <th>Iva</th>
                                                                <th>Importe</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td><label class="Label"
                                                                        style="font-weight: bold;">Precio</label></td>
                                                                <td><input id="NETO_REAL_TOTAL" type="text"
                                                                        class="form-control numero camposuma" disabled>
                                                                </td>
                                                                <td><input id="EXENTO_REAL_TOTAL" type="text"
                                                                        class="form-control numero camposuma" disabled>
                                                                </td>
                                                                <td><input id="IVA_REAL_TOTAL" type="text"
                                                                        class="form-control numero camposuma" disabled>
                                                                </td>
                                                                <td><input id="TOTAL_REAL_TOTAL" type="text"
                                                                        class="form-control numero camposuma" disabled>
                                                                </td>
                                                            </tr>
                                                            <tr style="display: none;">
                                                                <td><label class="Label"
                                                                        style="font-weight: bold;">Recargo</label></td>
                                                                <td><input id="txtRecargoNeto" class="input2 camposuma"
                                                                        disabled></td>
                                                                <td><input id="txtRecargoExento"
                                                                        class="input2 camposuma" disabled></td>
                                                                <td><input id="txtRecargoIva" class="input2 camposuma"
                                                                        disabled></td>
                                                                <td><input id="txtRecargoImpuesto"
                                                                        class="input2 camposuma" disabled></td>
                                                            </tr>
                                                            <tr>
                                                                <td><label class="Label"
                                                                        style="font-weight: bold;">Descuento</label>
                                                                </td>
                                                                <td><input id="NETO_DESCUENTO_TOTAL" type="text"
                                                                        class="form-control numero camposuma" disabled>
                                                                </td>
                                                                <td><input id="DESCUENTO_EXENTO_REAL" type="text"
                                                                        class="form-control numero camposuma" disabled>
                                                                </td>
                                                                <td><input id="IVA_DESCUENTO_TOTAL" type="text"
                                                                        class="form-control numero camposuma" disabled>
                                                                </td>
                                                                <td><input id="DESCUENTO_TOTAL" type="text"
                                                                        class="form-control numero camposuma" disabled>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="row justify-content-center">
                                    <div class="col-md-12 col-lg-12 d-grid gap-2">
                                        <label class="col-sm-12 control-label col-form-label"></label>
                                        <label class="col-sm-12 control-label col-form-label"></label>
                                        <button type="button" onclick="guardarcomprobante()"
                                            class=" btn btn-lg waves-effect waves-light btn-outline-info "> Grabar (F11)
                                        </button>
                                        <button type="button"
                                            class=" btn btn-lg waves-effect waves-light btn-outline-secondary">
                                            Cancela</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script src="../../wwwroot/abm/abm.js"></script>
<script src="../../wwwroot/ordencompras/ordencompras.js"></script>