<%- include('../abm/abm.ejs') %>

    <div class="modal fade" id="modal_DEPOSITOS" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-info text-white">
                    <h4 class="modal-title" id="info-header-modalLabel">
                        Deposito
                    </h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="card">
                        <form>
                            <fieldset id="solovista">
                                <div class="row DEPOSITOS">
                                    <div class="col-md-6" style="display: none;">
                                        <div class="form-floating mb-6">
                                            <input id="ID_CLAVE_PRIMARIA" name="ID_DEPOSITO" type="text"
                                                class="form-control" placeholder="Enter Name here">
                                            <label for="ID_DEPOSITO">ID_DEPOSITO</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <select id="SUCURSAL" name="ID_SUCURSAL" class="form-select mr-sm-2" required></select>
                                            <label for="ID_SUCURSAL">Sucursal</label>
                                            <small id="ID_SUCURSAL" class="invalid-feedback">Favor agregar la Sucursal</small>
                                        </div>
                                    </div> 
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input name="DESCRIPCION" maxlength="50" type="text" class="form-control" placeholder="Enter Name here" required>
                                            <label for="DESCRIPCION">Nombre Deposito</label>
                                            <sd-feemall id="DESCRIPCION" class="invalid-feedback">Favor agregar el Nombre Deposito</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="col-sm-12 control-label col-form-label" style="visibility: hidden;">Visible</label>
                                        <input type="checkbox" id="HABILITADO_FACTURACION" name="HABILITADO_FACTURACION" class="material-inputs filled-in chk-col-light-blue">
                                        <label for="HABILITADO_FACTURACION">Habilita Facturación</label>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <select name="ACTIVO" class="form-select mr-sm-2" required>
                                                <option value="1">Activo</option>
                                                <option value="0">Inactivo</option>
                                            </select>
                                            <label for="ACTIVO">Estado</label>
                                            <small id="ACTIVO" class="invalid-feedback">Favor agregar el Estado</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered display" id=""></table>
                                </div>
                            </fieldset>
                        </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        Cerrar
                    </button>
                    <button id="btnguardar" type="button" onclick="Funcionabm.guardarabm()"
                        class=" btn btn-light-info text-info font-weight-medium">
                        Guardar
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../../wwwroot/depositos/depositos.js"></script>