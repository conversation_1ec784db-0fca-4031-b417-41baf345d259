{"name": "staff_rayco", "version": "1.0.0", "description": "", "main": "index.mjs", "scripts": {"start": "node ./src/app.mjs", "dev": "nodemon ./src/app.mjs", "docker:run": "docker-compose down && docker-compose build --no-cache && docker-compose up -d && docker-compose logs -f", "docker:build": "docker-compose build --no-cache", "docker:up": "docker-compose up -d", "docker:logs": "docker-compose logs -f", "docker:down": "docker-compose down", "git:sync": "git pull origin main"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@mapbox/mbtiles": "^0.12.1", "axios": "^1.10.0", "body-parser": "^2.2.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^17.2.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-ejs-layouts": "^2.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "nodemon": "^3.1.10", "perfect-scrollbar": "^1.5.6"}}