var SessionTimeout = (function () {
  // BASE_URL se define en layout.ejs y está disponible globalmente
  var BASE_URL_SESSION = window.BASE_URL || `${location.protocol}//${location.host}`;
  var i = function () {
    $.sessionTimeout({
      title: "Notificacion ",
      message: "Tu sesión está por expirar, para seguir conectado debes de ingresar tu contraseña de vuelta.",
      logoutButton: "Desconectarse",
      keepAliveButton: "Seguir conectado",
      redirUrl: `${BASE_URL}/lockscreen`,
      logoutUrl: `${BASE_URL}/logout`,
      keepAliveUrl: `${BASE_URL}/lockscreen`,
      warnAfter: 1,
      redirAfter: 6000,
      ignoreUserActivity: !0,
      countdownMessage: "Redireccionando en {timer} segundos.",
      countdownBar: !0,
    });
  };
  return {
    init: function () {
      i();
    },
  };
})();

