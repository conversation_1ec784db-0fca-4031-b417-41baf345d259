<!DOCTYPE html>
<html dir="ltr" lang="es">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- Tell the browser to be responsive to screen width -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="keywords" content="Webapp" />
    <meta name="description" content="RayCo." />
    <meta name="robots" content="noindex,nofollow" />
    <title><%= process.env.APP_TITLE || 'Staff Ray Co.' %></title>
    <link rel="icon" type="image/png" sizes="<%= process.env.FAVICON_SIZE || '16x16' %>" href="/assets/images/marcas/raycoLogoR.png"/>
    <link href="/dist/css/style.min.css" rel="stylesheet" />
    <link href="/dist/libs/fontawesomepro/css/all.min.css" rel="stylesheet" />
    <style>
      @media (max-width: 768px) {
        .decorative-image-left {
          top: 5% !important;
          left: 5% !important;
          padding-left: 5px !important;
        }

        .decorative-image-left img {
          width: 120px !important;
        }

        .decorative-image-right {
          bottom: 5% !important;
          right: 5% !important;
          padding-right: 5px !important;
        }

        .decorative-image-right img {
          width: 100px !important;
        }
      }

      @media (max-width: 480px) {
        .decorative-image-left {
          top: 5% !important;
          left: 5% !important;
          padding-left: 5px !important;
        }

        .decorative-image-left img {
          width: 80px !important;
        }

        .decorative-image-right {
          bottom: 5% !important;
          right: 5% !important;
          padding-right: 5px !important;
        }

        .decorative-image-right img {
          width: 70px !important;
        }
      }
    </style>
  </head>

  <body>
    <div class="main-wrapper" style="background-color: #f5f5f5; min-height: 100vh;">

      <div class="auth-wrapper d-flex no-block justify-content-center align-items-center position-relative" style="min-height: 100vh;">

        <div class="auth-box on-sidebar p-4 bg-white m-0 shadow-lg" style="z-index: 10; border-radius: 15px; position: relative;">
          <div id="loginform">
            <div class="logo text-center">
              <span class="db">
                <img src="/assets/images/marcas/raycoDarkLogo.png" alt="logo" style="height: 150px;"/><br />
              </span>
            </div>
            <!-- Form -->
            <div class="row">
              <div class="col-12">
                <form class="form-horizontal mt-3 form-material" action="/login" method="POST">
                  <div class="form-group mb-3">
                    <div class="col-xs-12">
                      <input class="form-control" type="text" required="" name="USUARIO" placeholder="Nro. Documento"/>
                    </div>
                  </div>
                  <div class="form-group mb-3">
                    <div class="col-xs-12 position-relative">
                      <input class="form-control" type="password" required="" name="CLAVE_ACCESO" placeholder="Contraseña" id="password-field"/>
                      <span class="position-absolute" style="right: 15px; top: 50%; transform: translateY(-50%); cursor: pointer; z-index: 10;" id="toggle-password">
                        <i class="fa-solid fa-eye" id="eye-icon"></i>
                      </span>
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="d-flex">
                      <div class="checkbox checkbox-info pt-0">
                        <input id="checkbox-signup" type="checkbox" class="material-inputs chk-col-indigo"/>
                        <label for="checkbox-signup"> Recordar </label>
                      </div>
                      <div class="ms-auto">
                        <a onclick="redireccionRecuperar();" class="link font-weight-medium" style="cursor: pointer;">
                          <i class="fa fa-lock me-1"></i>
                          Solicitar Contraseña
                        </a>
                      </div>
                    </div>
                  </div>
                  <div class="form-group text-center my-3">
                    <div class="col-xs-12">
                      <button class="btn btn-info d-block w-100 waves-effect waves-light" type="submit">
                        Ingresar
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Incluir componente spinner -->
    <%- include('../../components/spinner.ejs') %>

    <script src="/dist/libs/jquery/dist/jquery.min.js"></script>
    <script src="/dist/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/dist/libs/sweetalert2/dist/sweetalert2.min.js"></script>
    <link rel="stylesheet" href="../../dist/libs/sweetalert2/dist/sweetalert2.min.css">
    <!-- Librería para hashing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <!-- Componente Spinner -->
    <script src="/wwwroot/components/spinner.js"></script>
    <script src="/wwwroot/login/login.js"></script>
    <!-- <script src="/dist/libs/fontawesomepro/js/all.min.js"></script> -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const togglePassword = document.getElementById('toggle-password');
        const passwordField = document.getElementById('password-field');
        const eyeIcon = document.getElementById('eye-icon');
        if (togglePassword && passwordField && eyeIcon) {
          togglePassword.addEventListener('click', function() {

            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);

            if (type === 'text') {
              eyeIcon.className = 'fa-solid fa-eye-slash';
            } else {
              eyeIcon.className = 'fa-solid fa-eye';
            }
          });
        } else {
          console.error('No se encontraron todos los elementos necesarios');
        }
      });
    </script>
  </body>
</html>
