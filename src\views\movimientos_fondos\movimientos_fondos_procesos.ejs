<style type="text/css">
    .select2-container--default .select2-selection--single{
        height: 59px !important;
    }
</style>
<div class="card-body">
    <div class="row">
        <div class="accordion mb-2" id="accordion-mov-fondo">
            <div class="accordion-item">
                <h2 class="accordion-header mb-2" id="flush-headingOne-mov-fondo">
                    <button class="accordion-button custom-info collapsed" type="button"
                        data-bs-toggle="collapse" data-bs-target="#flush-collapseOne-mov-fondo"
                        aria-expanded="false" aria-controls="flush-collapseOne">
                        Detalle Comprobante
                    </button>
                </h2>
                <div id="flush-collapseOne-mov-fondo" class="accordion-collapse collapse" aria-labelledby="flush-headingOne-mov-fondo" data-bs-parent="#accordion-mov-fondo">
                    <div class="col-md-12">
                        <div class="row">
                            <div id="comprobante-movimiento-fondo" class="col-md-9">
                                <%- include('../config_talonario/config_talonario.ejs') %>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating mb-2">
                                    <input id="FECHA_MOV_FONDO" type="date" class="form-control" required>
                                    <label for="FECHA_MOV_FONDO">Fecha Movimiento</label>
                                    <sd-feemall id="FECHA_MOV_FONDO" class="invalid-feedback">Favor agregar Fecha</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-2">
                                    <input id="CONCEPTO" type="text" class="form-control" placeholder="Enter Name here" required>
                                    <label for="CONCEPTO">Concepto</label>
                                    <sd-feemall id="CONCEPTO_FONDO" class="invalid-feedback">Favor agregar el Precio</small>
                                </div>
                            </div>
                        </div>
                    </div>                  
                </div>                     
            </div>
        </div>
        <div id="DEBE" class="col-md-12">                    
            <h4 class="card-title">Cuentas Debitar</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-floating mb-2">
                        <!-- <select id="ID_CUENTA_MOV_FONDO" class="w-100 form-control" multiple="multiple" data-placeholder="Escriba Cuenta" style="width: 100%; height: 36px"></select> -->
                        <!-- <label class="col-sm-12 control-label col-form-label">Cuentas de Fondos</label> -->
                        <select id="ID_CUENTA_MOV_FONDO" name="ID_CUENTA_MOV_FONDO" data-placeholder="Escriba Cuenta" class="select2 form-control custom-select" required> </select>
                        <sd-feemall id="ID_CUENTA_MOV_FONDO_" class="invalid-feedback">Favor agregar la Nro. Documento</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-floating mb-2">
                        <input id="PRECIO_MOV_FONDO" type="text" class="form-control numero" placeholder="Enter Name here" required>
                        <label for="PRECIO_MOV_FONDO">Monto</label>
                        <sd-feemall id="PRECIO_MOV_FONDO" class="invalid-feedback">Favor agregar el Precio</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-floating mb-2">
                        <input id="COTIZACION_MOV_FONDO" type="text" class="form-control numero" placeholder="Enter Name here" required disabled>
                        <label for="COTIZACION_MOV_FONDO">Cotización</label>
                        <sd-feemall id="COTIZACION_MOV_FONDO" class="invalid-feedback">Favor agregar el Precio</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-floating mb-2">
                        <input id="IMPORTE_MOV_FONDO" type="text" class="form-control numero" placeholder="Enter Name here" required disabled>
                        <label for="IMPORTE_MOV_FONDO">Importe</label>
                        <sd-feemall id="IMPORTE_MOV_FONDO" class="invalid-feedback">Favor agregar el Precio</small>
                    </div>
                </div>
                <div id="cupon" style="display: none;">
                    <div class="row">
                        <div class="col-md-2">                    
                            <div class="form-floating mb-2">
                                <input id="NUMERO_CUPON_MOV_FONDO" type="text" class="form-control" placeholder="Enter Name here" required>
                                <label for="NUMERO_CUPON_MOV_FONDO">Nro. Cupón</label>
                                <sd-feemall id="IMPORTE_MOV_FONDO" class="invalid-feedback">Favor agregar el Precio</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-floating mb-2">
                                <input id="AUTORIZACION_MOV_FONDO" type="text" class="form-control" placeholder="Enter Name here" required>
                                <label for="AUTORIZACION_MOV_FONDO">Nro. Autorización</label>
                                <sd-feemall id="AUTORIZACION_FONDO" class="invalid-feedback">Favor agregar el Precio</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-12 col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-end">
                                <!-- <button type="button" onclick="agregarcuentas('DEBE'); return false;" class="btn btn-info btn-rounded m-t-10 mb-2" data-bs-toggle="modal" data-bs-target="#add-contact">
                                    Agregar Cuenta
                                </button> -->
                                <button id="btnguardar" type="button" onclick="agregarcuentas('DEBE')" class=" btn btn-light-primary text-primary font-weight-medium">
                                    Agregar Cuenta
                                    <span class="mdi mdi-plus-box"></span>
                                </button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered display" id="GRILLA_CUENTAS_DEBITAR" style="width: 100%;">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>                   
        </div>
        <div id="HABER" class="col-md-12">                    
            <h4 class="card-title">Cuentas Acreditar</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-floating mb-2">
                        <!-- <select id="ID_CUENTA_MOV_FONDO" class="w-100 form-control" multiple="multiple" data-placeholder="Escriba Cuenta" style="width: 100%; height: 36px"></select> -->
                        <!-- <label class="col-sm-12 control-label col-form-label">Cuentas de Fondos</label> -->
                        <select id="ID_CUENTA_MOV_FONDO_HABER" name="ID_CUENTA_MOV_FONDO" data-placeholder="Escriba Cuenta" class="select2 form-control custom-select" required> </select>
                        <sd-feemall id="ID_CUENTA_MOV_FONDO_" class="invalid-feedback">Favor agregar la Nro. Documento</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-floating mb-2">
                        <input id="PRECIO_MOV_FONDO" type="text" class="form-control numero" placeholder="Enter Name here" required>
                        <label for="PRECIO_MOV_FONDO">Monto</label>
                        <sd-feemall id="PRECIO_MOV_FONDO" class="invalid-feedback">Favor agregar el Precio</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-floating mb-2">
                        <input id="COTIZACION_MOV_FONDO" type="text" class="form-control numero" placeholder="Enter Name here" required disabled>
                        <label for="COTIZACION_MOV_FONDO">Cotización</label>
                        <sd-feemall id="COTIZACION_MOV_FONDO" class="invalid-feedback">Favor agregar el Precio</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-floating mb-2">
                        <input id="IMPORTE_MOV_FONDO" type="text" class="form-control numero" placeholder="Enter Name here" required disabled>
                        <label for="IMPORTE_MOV_FONDO">Importe</label>
                        <sd-feemall id="IMPORTE_MOV_FONDO" class="invalid-feedback">Favor agregar el Precio</small>
                    </div>
                </div>
                <div class="col-lg-12 col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-end">
                                <!-- <button type="button" onclick="agregarcuentas('HABER'); return false;" class="btn btn-info btn-rounded m-t-10 mb-2" data-bs-toggle="modal" data-bs-target="#add-contact">
                                    Agregar Cuenta
                                </button> -->
                                <button id="btnguardar" type="button" onclick="agregarcuentas('HABER')"
                                    class=" btn btn-light-primary text-primary font-weight-medium">
                                    Agregar Cuenta
                                    <span class="mdi mdi-plus-box"></span>
                                </button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered display" id="GRILLA_CUENTAS_ACREDITAR" style="width: 100%;">
                                </table>
                            </div>
                        </div>
                    </div>
                </div> 
            </div>
        </div>
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-2">
                    <div class="form-floating mb-2">
                        <input id="TOTAL" type="text" class="form-control numero" placeholder="Enter Name here" required disabled>
                        <label for="TOTAL">Total</label>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-floating mb-2">
                        <input id="SALDO" type="text" class="form-control numero" placeholder="Enter Name here" required disabled>
                        <label for="SALDO">Saldo</label>
                    </div>
                </div>                
                <div class="col-md-8">
                    <div class="text-end">
                        <button class="dt-button btn_nuevo btn waves-effect waves-light btn-outline-secondary" tabindex="0" aria-controls="tbgrilla" onclick="cancelarMovimientoSemi();return false">
                            <span>Cancelar Movimiento <i class="me-2 mdi mdi-delete-empty"></i></span>
                        </button>
                        <button class="dt-button btn_nuevo btn waves-effect waves-light btn-outline-info" tabindex="0" aria-controls="tbgrilla" onclick="guardarMovimientoSemi();return false">
                            <span>Procesar Movimiento <i class="me-2 mdi mdi-content-save-all"></i></span>
                        </button>
                        <!-- <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                            Cancelar
                        </button>
                        <button id="btnguardar" type="button" onclick="Funcionabm.guardarabm()"
                            class=" btn btn-light-info text-info font-weight-medium">
                            Procesar
                        </button> -->
                    </div>                    
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../../wwwroot/movimientos_fondos/movimientos_fondos_procesos.js"></script>