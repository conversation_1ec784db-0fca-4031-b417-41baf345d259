<%- include('../abm/abm.ejs') %>

    <div class="modal fade" id="modal_METODOS_QUERY" data-bs-backdrop="static" data-bs-keyboard="false"
        tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-info text-white">
                    <h4 class="modal-title" id="info-header-modalLabel">
                        Metodos Query
                    </h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="card">
                        <form>
                            <fieldset id="solovista">
                                <div class="row METODOS_QUERY">
                                    <div class="col-md-6" style="display: none;">
                                        <div class="form-floating mb-3">
                                            <input id="ID_CLAVE_PRIMARIA" name="ID_METODO_QUERY" type="text"
                                                class="form-control" placeholder="Enter Name here">
                                            <label for="ID_METODO_QUERY">ID_METODO_QUERY</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input name="NOMBRE" type="text" class="form-control"
                                                placeholder="Enter Name here" required>
                                            <label for="NOMBRE">Nombre</label>
                                            <sd-feemall id="NOMBRE" class="invalid-feedback">Favor agregar el
                                                Nombre</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select name="TIPO" class="form-select mr-sm-2" required>
                                                <option value="QUERY">Query</option>
                                                <option value="STORE_PROCEDURE" selected>Procedimiento</option>
                                            </select>
                                            <label for="TIPO">Tipo Query</label>
                                            <small id="TIPO" class="invalid-feedback">Favor agregar el Tipo Query</small>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="form-floating mb-3">
                                            <textarea id="txtSSQL" name="SSQL" class="form-control" rows="3" placeholder="SSQL"></textarea>
                                            <label for="SSQL">Consulta</label>
                                            <small id="SSQL" class="invalid-feedback">Favor agregar la
                                                Consulta</small>
                                        </div>
                                    </div> 
                                    <div class="col-md-4" style="visibility: hidden;">
                                        <div class="form-floating mb-3">
                                            <input  type="text" class="form-control" placeholder="Enter Name here">
                                            <!-- <label for="ID_METODO_QUERY">ID_METODO_QUERY</label> -->
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <textarea id="txtDESCRIPCION" name="SSQL" class="form-control" rows="3" placeholder="SSQL"></textarea>
                                            <label for="DESCRIPCION">Observación</label>
                                            <small id="DESCRIPCION" class="invalid-feedback">Favor agregar la
                                                Observación</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="col-sm-12 control-label col-form-label" style="visibility: hidden;">Requieren Token</label>
                                        <input type="checkbox" id="REQUIERE_TOKEN" name="REQUIERE_TOKEN" class="material-inputs filled-in chk-col-light-blue">
                                        <label for="REQUIERE_TOKEN">Requiere Token</label>
                                    </div> 
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select name="ACTIVO" class="form-select mr-sm-2" required>
                                                <option value="1">Activo</option>
                                                <option value="0">Inactivo</option>
                                            </select>
                                            <label for="ACTIVO">Estado</label>
                                            <small id="ACTIVO" class="invalid-feedback">Favor agregar el Estado</small>
                                        </div>
                                    </div>
                                </div>
                                <fieldset id="solovista">
                                    <!-- Nav tabs -->
                                    <ul class="nav nav-tabs" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" data-bs-toggle="tab" href="#cliente" role="tab">
                                                <span>Parametros Query</span>
                                            </a>
                                        </li>
                                    </ul>
                                    <!-- Tab panes -->
                                    <div class="tab-content">
                                        <div class="tab-pane p-3 active" id="cliente" role="tabpanel">
                                            <div class="row DET_METODOS_QUERY">
                                                <div class="col-md-2">
                                                    <div class="form-group">
                                                        <label class="col-sm-12 control-label col-form-label">Parametro</label>
                                                        <input name="NOMBRE" type="text" class="form-control text-uppercase" aria-describedby="maxval" required>
                                                        <sd-feemall id="NOMBRE" class="invalid-feedback">Favor agregar el Parametro</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="form-group">
                                                        <label class="col-sm-12 control-label col-form-label">Tipo de Dato</label>
                                                        <select name="ACTIVO" class="form-select mr-sm-2" required>
                                                            <option value="1">Activo</option>
                                                            <option value="0">Inactivo</option>
                                                        </select>
                                                        <sd-feemall id="NOMBRE" class="invalid-feedback">Favor agregar el Parametro</small>
                                                    </div>
                                                </div>
                                                <div class="col-lg-12 col-md-12">
                                                    <div class="card">
                                                        <div class="card-body">
                                                            <div class="table-responsive">
                                                                <table class="table table-striped table-bordered display" id="PERFILES_COMP_TIPO_COMP" style="width: 100%;"></table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </fieldset>
                        </form>
                        <%- include('../abm/modal-footer.ejs') %>
                    </div>
                </div>
            </div>
        </div>
    </div>
