<link rel="stylesheet" href="../../wwwroot/estadocuenta/estadocuenta.css">

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 id="nomform" class="text-themecolor mb-0"></h3>
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/home">Home</a>
            </li>
            <li id="nomformbread" class="breadcrumb-item active"></li>
        </ol>
    </div>
</div>

<!-- Loading State -->
<div id="loadingState" class="d-flex justify-content-center align-items-center" style="min-height: 300px;">
    <div class="text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Cargando...</span>
        </div>
        <p class="mt-2">Cargando datos...</p>
    </div>
</div>

<!-- Error State -->
<div id="errorState" class="d-none">
    <div class="alert alert-danger text-center">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <span id="errorMessage">Error al cargar los datos</span>
        <button class="btn btn-primary btn-sm ms-3" onclick="init()">Reintentar</button>
    </div>
</div>

<!-- Inactive Account State -->
<div id="inactiveState" class="d-none">
    <div class="d-flex justify-content-center align-items-center" style="min-height: 300px;">
        <div class="text-center">
            <i class="fas fa-user-slash fa-3x text-danger mb-3"></i>
            <h4 class="text-danger mb-3">Cuenta Inactiva</h4>
            <p class="text-muted mb-4">Tu Cuenta Corriente no se encuentra activa.<br>Por favor, contacta a R.R.H.H. para más información.</p>
            <div class="d-flex gap-2 justify-content-center">
                <button id="verify-status-btn" class="btn btn-outline-primary" onclick="verificarEstadoCuenta()">
                    <i class="fas fa-sync-alt me-2"></i>Verificar Estado
                </button>
            </div>
        </div>
    </div>
</div>

<div class="card-body">
    <!-- Main Content -->
    <div id="mainContent" class="d-none">
        <!-- Estado de Cuenta Tabbed Card -->
        <div class="card rounded-3 overflow-hidden">
            <div class="card-body p-3 p-md-4 p-lg-2 p-xl-1">
                <div class="py-2 px-3 fw-bold d-flex align-items-center estadoCuentaTitle">
                    <span>Movimientos: Estado de Cuenta</span>
                </div>

                <!-- Custom Modern Tabs -->
                <div class="d-flex justify-content-center mb-4 gap-2 gap-md-3">
                    <button type="button" id="creditoTab"
                            class="d-flex align-items-center gap-2 px-4 py-2 rounded-pill fw-bold border-0 shadow-sm position-relative tabPillBtnCustom tab-pill-btn tabPillBtnActive"
                            onclick="switchTab('credito')" aria-selected="true" aria-controls="credito" tabindex="0">
                        <i class="fas fa-credit-card estadoCuentaTabIcon"></i>
                        <span>Crédito</span>
                    </button>
                    <button type="button" id="contadoTab"
                            class="d-flex align-items-center gap-2 px-4 py-2 rounded-pill fw-bold border-0 shadow-sm position-relative tabPillBtnCustom tab-pill-btn tabPillBtnInactive"
                            onclick="switchTab('contado')" aria-selected="false" aria-controls="contado" tabindex="0">
                        <i class="fas fa-cash-register estadoCuentaTabIcon"></i>
                        <span>Contado</span>
                    </button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Credit Tab -->
                    <div id="credito" class="tab-pane p-2 p-md-3 p-lg-1 p-xl-0 active show" role="tabpanel">
                        <div class="container-fluid px-0">
                            <!-- Summary Cards -->
                            <div id="creditSummaryCards" class="row g-2 g-md-3 g-lg-4 mb-1 justify-content-center">
                                <!-- Cards will be populated by JavaScript -->
                            </div>

                            <!-- Progress Bar -->
                            <div id="creditProgressBar" class="d-flex justify-content-center mb-2 mb-md-3">
                                <!-- Progress bar will be populated by JavaScript -->
                            </div>

                            <!-- Movement Table -->
                            <div id="creditMovementTable">
                                <!-- Movement table will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Cash Tab -->
                    <div id="contado" class="tab-pane p-2 p-md-3 p-lg-1 p-xl-0 d-none" role="tabpanel">
                        <div class="container-fluid px-0">
                            <!-- Summary Cards -->
                            <div id="cashSummaryCards" class="row g-2 g-md-3 g-lg-4 mb-1 justify-content-center">
                                <!-- Cards will be populated by JavaScript -->
                            </div>

                            <!-- Progress Bar -->
                            <div id="cashProgressBar" class="d-flex justify-content-center mb-2 mb-md-3">
                                <!-- Progress bar will be populated by JavaScript -->
                            </div>

                            <!-- Movement Table -->
                            <div id="cashMovementTable">
                                <!-- Movement table will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../../wwwroot/estadocuenta/estadocuenta.js"></script>