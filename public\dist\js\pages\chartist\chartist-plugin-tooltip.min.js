!(function (e, o) {
  "function" == typeof define && define.amd
    ? define(["chartist"], function (t) {
        return (e.returnExportsGlobal = o(t));
      })
    : "object" == typeof exports
    ? (module.exports = o(require("chartist")))
    : (e["Chartist.plugins.tooltips"] = o(Chartist));
})(this, function (t) {
  return (
    (function (l, y, v) {
      "use strict";
      var t = {
        currency: void 0,
        currencyFormatCallback: void 0,
        tooltipOffset: { x: 0, y: -20 },
        anchorToPoint: !1,
        appendToBody: !1,
        class: void 0,
        pointClass: "ct-point",
      };
      function o(t) {
        var e = new RegExp("tooltip-show\\s*", "gi");
        t.className = t.className.replace(e, "").trim();
      }
      function x(t, e) {
        return (
          -1 < (" " + t.getAttribute("class") + " ").indexOf(" " + e + " ")
        );
      }
      (v.plugins = v.plugins || {}),
        (v.plugins.tooltip = function (h) {
          return (
            (h = v.extend({}, t, h)),
            function (f) {
              var t = h.pointClass;
              f instanceof v.Bar
                ? (t = "ct-bar")
                : f instanceof v.Pie &&
                  (t = f.options.donut ? "ct-slice-donut" : "ct-slice-pie");
              var c = f.container,
                u = c.querySelector(".chartist-tooltip");
              u ||
                (((u = y.createElement("div")).className = h.class
                  ? "chartist-tooltip " + h.class
                  : "chartist-tooltip"),
                h.appendToBody ? y.body.appendChild(u) : c.appendChild(u));
              var d = u.offsetHeight,
                g = u.offsetWidth;
              function e(t, e, o) {
                c.addEventListener(t, function (t) {
                  (e && !x(t.target, e)) || o(t);
                });
              }
              function m(t) {
                d = d || u.offsetHeight;
                var e,
                  o,
                  n = -(g = g || u.offsetWidth) / 2 + h.tooltipOffset.x,
                  i = -d + h.tooltipOffset.y;
                if (h.appendToBody)
                  (u.style.top = t.pageY + i + "px"),
                    (u.style.left = t.pageX + n + "px");
                else {
                  var a = c.getBoundingClientRect(),
                    r = t.pageX - a.left - l.pageXOffset,
                    s = t.pageY - a.top - l.pageYOffset;
                  !0 === h.anchorToPoint &&
                    t.target.x2 &&
                    t.target.y2 &&
                    ((e = parseInt(t.target.x2.baseVal.value)),
                    (o = parseInt(t.target.y2.baseVal.value))),
                    (u.style.top = (o || s) + i + "px"),
                    (u.style.left = (e || r) + n + "px");
                }
              }
              o(u),
                e("mouseover", t, function (t) {
                  var e,
                    o,
                    n = t.target,
                    i = "",
                    a = (f instanceof v.Pie ? n : n.parentNode)
                      ? n.parentNode.getAttribute("ct:meta") ||
                        n.parentNode.getAttribute("ct:series-name")
                      : "",
                    r = n.getAttribute("ct:meta") || a || "",
                    s = !!r,
                    c = n.getAttribute("ct:value");
                  if (
                    (h.transformTooltipTextFnc &&
                      "function" == typeof h.transformTooltipTextFnc &&
                      (c = h.transformTooltipTextFnc(c)),
                    h.tooltipFnc && "function" == typeof h.tooltipFnc)
                  )
                    i = h.tooltipFnc(r, c);
                  else {
                    if (h.metaIsHTML) {
                      var l = y.createElement("textarea");
                      (l.innerHTML = r), (r = l.value);
                    }
                    if (
                      ((r =
                        '<span class="chartist-tooltip-meta">' + r + "</span>"),
                      s)
                    )
                      i += r + "<br>";
                    else if (f instanceof v.Pie) {
                      var p = (function (t, e) {
                        for (; (t = t.nextSibling), t && !x(t, e); );
                        return t;
                      })(n, "ct-label");
                      p && (i += ((e = p).innerText || e.textContent) + "<br>");
                    }
                    c &&
                      (h.currency &&
                        (c =
                          null != h.currencyFormatCallback
                            ? h.currencyFormatCallback(c, h)
                            : h.currency +
                              c.replace(/(\d)(?=(\d{3})+(?:\.\d+)?$)/g, "$1,")),
                      (i += c =
                        '<span class="chartist-tooltip-value">' +
                        c +
                        "</span>"));
                  }
                  i &&
                    ((u.innerHTML = i),
                    m(t),
                    x((o = u), "tooltip-show") ||
                      (o.className = o.className + " tooltip-show"),
                    (d = u.offsetHeight),
                    (g = u.offsetWidth));
                }),
                e("mouseout", t, function () {
                  o(u);
                }),
                e("mousemove", null, function (t) {
                  !1 === h.anchorToPoint && m(t);
                });
            }
          );
        });
    })(window, document, t),
    t.plugins.tooltips
  );
});
