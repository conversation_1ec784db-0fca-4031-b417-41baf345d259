/*************************************************************************************/
// -->Template Name: Bootstrap Press Admin
// -->Author: Themedesigner
// -->Email: <EMAIL>
// -->File: c3_chart_JS
/*************************************************************************************/
$(function () {
  var a = c3.generate({
    bindto: "#rotated-axis",
    size: { height: 400 },
    color: { pattern: ["#00acc1", "#1e88e5"] },
    data: {
      columns: [
        ["data1", 50, 250, 90, 400, 300, 150],
        ["data2", 30, 100, 85, 50, 15, 25],
      ],
      types: { data1: "bar" },
    },
    axis: { rotated: !0 },
    grid: { y: { show: !0 } },
  });
});
