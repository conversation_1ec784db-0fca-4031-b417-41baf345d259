<%- include('../abm/abm.ejs') %>

    <div class="modal fade" id="modal_DIRECCIONES" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-info text-white">
                    <h4 class="modal-title" id="info-header-modalLabel">
                        Direcciones
                    </h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="card">
                        <form>
                            <fieldset id="solovista">
                                <div class="row DIRECCIONES">
                                    <div class="col-md-6" style="display: none;">
                                        <div class="form-floating mb-3">
                                            <input id="ID_CLAVE_PRIMARIA" name="ID_DIRECCION" type="text"
                                                class="form-control" placeholder="Enter Name here">
                                            <label for="ID_DIRECCION">ID_Direccion</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input name="DIRECCION" type="text" class="form-control"
                                                placeholder="Enter Name here" required>
                                            <label for="DIRECCION">Direccion</label>
                                            <sd-feemall id="DIRECCION" class="invalid-feedback">Favor agregar la
                                                direccion</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select id="ddl_ID_PAIS" name="ID_PAIS" class="form-select mr-sm-2" required>
                                            </select>
                                            <label for="ID_PAIS">Pais</label>
                                            <small id="ID_PAIS" class="invalid-feedback">
                                                Favor agregar la Pais
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select id="ddl_ID_DEPARTAMENTO" name="ID_DEPARTAMENTO" class="form-select mr-sm-2" required>
                                            </select>
                                            <label for="ID_DEPARTAMENTO">Departamento</label>
                                            <small id="ID_DEPARTAMENTO" class="invalid-feedback">
                                                Favor agregar la Departamento
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select id="ddl_ID_CIUDAD" name="ID_CIUDAD" class="form-select mr-sm-2" required>
                                            </select>
                                            <label for="ID_CIUDAD">Ciudad</label>
                                            <small id="ID_CIUDAD" class="invalid-feedback">
                                                Favor agregar la Ciudad
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select id="ddl_ID_BARRIO" name="ID_BARRIO" class="form-select mr-sm-2" required>
                                            </select>
                                            <label for="ID_BARRIO">Barrio</label>
                                            <small id="ID_BARRIO" class="invalid-feedback">
                                                Favor agregar la Barrio
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select name="ACTIVO" class="form-select mr-sm-2" required>
                                                <option value="1" selected>Activo</option>
                                                <option value="0">Inactivo</option>
                                            </select>
                                            <label for="ACTIVO">Estado</label>
                                            <small id="ACTIVO" class="invalid-feedback">Favor agregar el Estado</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input name="CROQUIS_DIRECCION" type="text" class="form-control"
                                                placeholder="Enter Name here" required>
                                            <label for="CROQUIS_DIRECCION">Croquis Direccion</label>
                                            <sd-feemall id="CROQUIS_DIRECCION" class="invalid-feedback">Favor agregar Croquis Direccion</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input name="CROQUIS_LATITUD" type="text" class="form-control"
                                                placeholder="Enter Name here" required>
                                            <label for="CROQUIS_LATITUD">Croquis Latitud</label>
                                            <sd-feemall id="CROQUIS_LATITUD" class="invalid-feedback">Favor agregar Croquis Latitud</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input name="CROQUIS_LONGITUD" type="text" class="form-control"
                                                placeholder="Enter Name here" required>
                                            <label for="CROQUIS_LONGITUD">Croquis Longitud</label>
                                            <sd-feemall id="CROQUIS_LONGITUD" class="invalid-feedback">Favor agregar Croquis Longitud</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <div id="map"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input name="REFERENCIAS_UBICACION" type="text" class="form-control"
                                                placeholder="Enter Name here" required>
                                            <label for="REFERENCIAS_UBICACION">Ubicacion de Referencia</label>
                                            <sd-feemall id="REFERENCIAS_UBICACION" class="invalid-feedback">Favor agregar la Ubicacion de Referencia</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input name="TELEFONO" type="text" class="form-control" placeholder="Enter Name here" required>
                                            <label for="TELEFONO">Telefono de Referencia</label>
                                            <sd-feemall id="TELEFONO" class="invalid-feedback">Favor agregar la Ubicacion de Referencia</small>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </form>
                        <%- include('../abm/modal-footer.ejs') %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../wwwroot/direcciones/direcciones.js"></script>