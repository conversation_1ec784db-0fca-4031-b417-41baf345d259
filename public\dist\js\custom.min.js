$(function() {
    "use strict";
    feather.replace(),
    $(".preloader").fadeOut(),
    $(".left-sidebar").hover(function() {
        $(".navbar-header").addClass("expand-logo")
    }, function() {
        $(".navbar-header").removeClass("expand-logo")
    }),
    $(".nav-toggler").on("click", function() {
        $("#main-wrapper").toggleClass("show-sidebar"),
        $(".nav-toggler i").toggleClass("ti-menu")
    }),
    $(".nav-lock").on("click", function() {
        $("body").toggleClass("lock-nav"),
        $(".nav-lock i").toggleClass("mdi-toggle-switch-off"),
        $("body, .page-wrapper").trigger("resize")
    }),
    $(".search-box a, .search-box .app-search .srh-btn").on("click", function() {
        $(".app-search").toggle(200),
        $(".app-search select2").focus()
    }),
    $(function() {
        $(".service-panel-toggle").on("click", function() {
            $(".customizer").toggleClass("show-service-panel")
        }),
        $(".page-wrapper").on("click", function() {
            $(".customizer").removeClass("show-service-panel")
        })
    }),
    $(".floating-labels .form-control").on("focus blur", function(e) {
        $(this).parents(".form-group").toggleClass("focused", "focus" === e.type || 0 < this.value.length)
    }).trigger("blur"),
    $(function() {
        [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(e) {
            return new bootstrap.Tooltip(e)
        })
    }),
    $(function() {
        [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).map(function(e) {
            return new bootstrap.Popover(e)
        })
    }),
    $(".message-center, .customizer-body, .scrollable").perfectScrollbar({
        wheelPropagation: !0
    }),
    $("body, .page-wrapper").trigger("resize"),
    $(".page-wrapper").delay(20).show(),
    $(".list-task li label").click(function() {
        $(this).toggleClass("task-done")
    }),
    $('a[data-action="collapse"]').on("click", function(e) {
        e.preventDefault(),
        $(this).closest(".card").find('[data-action="collapse"] i').toggleClass("ti-minus ti-plus"),
        $(this).closest(".card").children(".card-body").collapse("toggle")
    }),
    $('a[data-action="expand"]').on("click", function(e) {
        e.preventDefault(),
        $(this).closest(".card").find('[data-action="expand"] i').toggleClass("mdi-arrow-expand mdi-arrow-compress"),
        $(this).closest(".card").toggleClass("card-fullscreen")
    }),
    $('a[data-action="close"]').on("click", function() {
        $(this).closest(".card").removeClass().slideUp("fast")
    }),
    $(".mega-dropdown").on("click", function(e) {
        e.stopPropagation()
    }),
    $("#monthchart").sparkline([5, 6, 2, 9, 4, 7, 10, 12], {
        type: "bar",
        height: "35",
        barWidth: "4",
        resize: !0,
        barSpacing: "4",
        barColor: "#1e88e5"
    }),
    $("#lastmonthchart").sparkline([5, 6, 2, 9, 4, 7, 10, 12], {
        type: "bar",
        height: "35",
        barWidth: "4",
        resize: !0,
        barSpacing: "4",
        barColor: "#7460ee"
    }),
    $(".show-left-part").on("click", function() {
        $(".left-part").toggleClass("show-panel"),
        $(".show-left-part").toggleClass("ti-menu")
    }),
    $(".custom-file-input").on("change", function() {
        var e = $(this).val();
        $(this).next(".custom-file-label").html(e)
    })
});
