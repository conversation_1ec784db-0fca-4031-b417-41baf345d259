$(function () {
  "use strict";
  //Simple line chart

  new Chartist.Line(
    ".ct-sm-line-chart",
    {
      labels: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      series: [
        [12, 9, 7, 8, 5],
        [2, 1, 3.5, 7, 3],
        [1, 3, 4, 5, 6],
      ],
    },
    {
      fullWidth: true,

      plugins: [Chartist.plugins.tooltip()],
      chartPadding: {
        right: 40,
      },
    }
  );

  // line chart with area

  new Chartist.Line(
    ".ct-area-ln-chart",
    {
      labels: [1, 2, 3, 4, 5, 6, 7, 8],
      series: [[5, 9, 7, 8, 5, 3, 5, 4]],
    },
    {
      low: 0,

      plugins: [Chartist.plugins.tooltip()],
      showArea: true,
    }
  );

  // ct-polar-chart
  new Chartist.Line(
    "#ct-polar-chart",
    {
      labels: [1, 2, 3, 4, 5, 6, 7, 8],
      series: [
        [1, 2, 3, 1, -2, 0, 1, 0],
        [-2, -1, -2, -1, -2.5, -1, -2, -1],
        [0, 0, 0, 1, 2, 2.5, 2, 1],
        [2.5, 2, 1, 0.5, 1, 0.5, -1, -2.5],
      ],
    },
    {
      high: 3,
      low: -3,
      chartPadding: {
        left: -20,
        top: 10,
      },
      showArea: true,
      showLine: false,
      showPoint: true,
      fullWidth: true,
      plugins: [Chartist.plugins.tooltip()],
      axisX: {
        showLabel: true,
        showGrid: true,
      },
      axisY: {
        showLabel: false,
        showGrid: true,
      },
    }
  );

  // ct-animation-chart

  var chart = new Chartist.Line(
    ".ct-animation-chart",
    {
      labels: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"],
      series: [
        [12, 9, 7, 8, 5, 4, 6, 2, 3, 3, 4, 6],
        [4, 5, 3, 7, 3, 5, 5, 3, 4, 4, 5, 5],
        [5, 3, 4, 5, 6, 3, 3, 4, 5, 6, 3, 4],
      ],
    },
    {
      low: 0,
    }
  );

  // Let's put a sequence number aside so we can use it in the event callbacks
  var seq = 0,
    delays = 80,
    durations = 500;

  // Once the chart is fully created we reset the sequence
  chart.on("created", function () {
    seq = 0;
  });

  // On each drawn element by Chartist we use the Chartist.Svg API to trigger SMIL animations
  chart.on("draw", function (data) {
    seq++;

    if (data.type === "line") {
      // If the drawn element is a line we do a simple opacity fade in. This could also be achieved using CSS3 animations.
      data.element.animate({
        opacity: {
          // The delay when we like to start the animation
          begin: seq * delays + 1000,
          // Duration of the animation
          dur: durations,
          // The value where the animation should start
          from: 0,
          // The value where it should end
          to: 1,
        },
      });
    } else if (data.type === "label" && data.axis === "x") {
      data.element.animate({
        y: {
          begin: seq * delays,
          dur: durations,
          from: data.y + 100,
          to: data.y,
          // We can specify an easing function from Chartist.Svg.Easing
          easing: "easeOutQuart",
        },
      });
    } else if (data.type === "label" && data.axis === "y") {
      data.element.animate({
        x: {
          begin: seq * delays,
          dur: durations,
          from: data.x - 100,
          to: data.x,
          easing: "easeOutQuart",
        },
      });
    } else if (data.type === "point") {
      data.element.animate({
        x1: {
          begin: seq * delays,
          dur: durations,
          from: data.x - 10,
          to: data.x,
          easing: "easeOutQuart",
        },
        x2: {
          begin: seq * delays,
          dur: durations,
          from: data.x - 10,
          to: data.x,
          easing: "easeOutQuart",
        },
        opacity: {
          begin: seq * delays,
          dur: durations,
          from: 0,
          to: 1,
          easing: "easeOutQuart",
        },
      });
    } else if (data.type === "grid") {
      // Using data.axis we get x or y which we can use to construct our animation definition objects
      var pos1Animation = {
        begin: seq * delays,
        dur: durations,
        from: data[data.axis.units.pos + "1"] - 30,
        to: data[data.axis.units.pos + "1"],
        easing: "easeOutQuart",
      };

      var pos2Animation = {
        begin: seq * delays,
        dur: durations,
        from: data[data.axis.units.pos + "2"] - 100,
        to: data[data.axis.units.pos + "2"],
        easing: "easeOutQuart",
      };

      var animations = {};
      animations[data.axis.units.pos + "1"] = pos1Animation;
      animations[data.axis.units.pos + "2"] = pos2Animation;
      animations["opacity"] = {
        begin: seq * delays,
        dur: durations,
        from: 0,
        to: 1,
        easing: "easeOutQuart",
      };

      data.element.animate(animations);
    }
  });

  // For the sake of the example we update the chart every time it's created with a delay of 10 seconds
  chart.on("created", function () {
    if (window.__exampleAnimateTimeout) {
      clearTimeout(window.__exampleAnimateTimeout);
      window.__exampleAnimateTimeout = null;
    }
    window.__exampleAnimateTimeout = setTimeout(
      chart.update.bind(chart),
      12000
    );
  });

  // SVG Path animation graph
  var chart = new Chartist.Line(
    ".ct-svg-chart",
    {
      labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
      series: [
        [1, 5, 2, 5, 4, 3],
        [2, 3, 4, 8, 1, 2],
        [5, 4, 3, 2, 1, 0.5],
      ],
    },
    {
      low: 0,
      showArea: true,
      showPoint: false,
      fullWidth: true,
    }
  );

  chart.on("draw", function (data) {
    if (data.type === "line" || data.type === "area") {
      data.element.animate({
        d: {
          begin: 2000 * data.index,
          dur: 2000,
          from: data.path
            .clone()
            .scale(1, 0)
            .translate(0, data.chartRect.height())
            .stringify(),
          to: data.path.clone().stringify(),
          easing: Chartist.Svg.Easing.easeOutQuint,
        },
      });
    }
  });

  // Bar chart

  var data = {
    labels: [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ],
    series: [
      [5, 4, 3, 7, 5, 10, 3, 4, 8, 10, 6, 8],
      [3, 2, 9, 5, 4, 6, 4, 6, 7, 8, 7, 4],
    ],
  };

  var options = {
    seriesBarDistance: 10,
  };

  var responsiveOptions = [
    [
      "screen and (max-width: 640px)",
      {
        seriesBarDistance: 5,

        axisX: {
          labelInterpolationFnc: function (value) {
            return value[0];
          },
        },
      },
    ],
  ];

  new Chartist.Bar(".ct-bar-chart", data, options, responsiveOptions);

  // ct-gauge-chart

  new Chartist.Pie(
    ".ct-gauge-chart",
    {
      series: [20, 10, 30, 40],
    },
    {
      donut: true,
      donutWidth: 60,
      startAngle: 270,
      total: 200,
      low: 0,
      showLabel: false,
    }
  );

  // Animated Donute chart
  var chart = new Chartist.Pie(
    ".ct-donute-chart",
    {
      series: [10, 20, 50, 20, 5, 50, 15],
      labels: [1, 2, 3, 4, 5, 6, 7],
    },
    {
      donut: true,
      showLabel: false,
    }
  );

  chart.on("draw", function (data) {
    if (data.type === "slice") {
      // Get the total path length in order to use for dash array animation
      var pathLength = data.element._node.getTotalLength();

      // Set a dasharray that matches the path length as prerequisite to animate dashoffset
      data.element.attr({
        "stroke-dasharray": pathLength + "px " + pathLength + "px",
      });

      // Create animation definition while also assigning an ID to the animation for later sync usage
      var animationDefinition = {
        "stroke-dashoffset": {
          id: "anim" + data.index,
          dur: 1000,
          from: -pathLength + "px",
          to: "0px",
          easing: Chartist.Svg.Easing.easeOutQuint,
          // We need to use `fill: 'freeze'` otherwise our animation will fall back to initial (not visible)
          fill: "freeze",
        },
      };

      // If this was not the first slice, we need to time the animation so that it uses the end sync event of the previous animation
      if (data.index !== 0) {
        animationDefinition["stroke-dashoffset"].begin =
          "anim" + (data.index - 1) + ".end";
      }

      // We need to set an initial value before the animation starts as we are not in guided mode which would do that for us
      data.element.attr({
        "stroke-dashoffset": -pathLength + "px",
      });

      // We can't use guided mode as the animations need to rely on setting begin manually
      // See http://gionkunz.github.io/chartist-js/api-documentation.html#chartistsvg-function-animate
      data.element.animate(animationDefinition, false);
    }
  });

  // For the sake of the example we update the chart every time it's created with a delay of 8 seconds
  chart.on("created", function () {
    if (window.__anim21278907124) {
      clearTimeout(window.__anim21278907124);
      window.__anim21278907124 = null;
    }
    window.__anim21278907124 = setTimeout(chart.update.bind(chart), 10000);
  });
});
