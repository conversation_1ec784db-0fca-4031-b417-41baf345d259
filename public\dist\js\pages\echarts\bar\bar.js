$(function () {
  "use strict";
  // ------------------------------
  // Basic bar chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var myChart = echarts.init(document.getElementById("basic-bar"));

  // specify chart configuration item and data
  var option = {
    // Setup grid
    grid: {
      left: "1%",
      right: "2%",
      bottom: "3%",
      containLabel: true,
    },

    // Add Tooltip
    tooltip: {
      trigger: "axis",
    },

    legend: {
      data: ["Site A", "Site B"],
    },
    toolbox: {
      show: true,
      feature: {
        magicType: { show: true, type: ["line", "bar"] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    color: ["#1e88e5", "#00acc1"],
    calculable: true,
    xAxis: [
      {
        type: "category",
        data: [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "July",
          "Aug",
          "Sept",
          "Oct",
          "Nov",
          "Dec",
        ],
      },
    ],
    yAxis: [
      {
        type: "value",
        borderColor: "rgba(0,0,0,.1)",
      },
    ],
    series: [
      {
        name: "Site A",
        type: "bar",
        data: [
          2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3,
        ],
        markPoint: {
          data: [
            { type: "max", name: "Max" },
            { type: "min", name: "Min" },
          ],
        },
        markLine: {
          data: [{ type: "average", name: "Average" }],
        },
      },
      {
        name: "Site B",
        type: "bar",
        data: [
          2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3,
        ],
        markPoint: {
          data: [
            {
              name: "The highest year",
              value: 182.2,
              xAxis: 7,
              yAxis: 183,
              symbolSize: 18,
            },
            { name: "Year minimum", value: 2.3, xAxis: 11, yAxis: 3 },
          ],
        },
        markLine: {
          data: [{ type: "average", name: "Average" }],
        },
      },
    ],
  };
  // use configuration item and data specified to show chart
  myChart.setOption(option);

  // ------------------------------
  // Stacked bar chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var stackedChart = echarts.init(document.getElementById("stacked-bar"));

  // specify chart configuration item and data
  var option = {
    // Setup grid
    grid: {
      x: 40,
      x2: 40,
      y: 45,
      y2: 25,
    },

    // Add tooltip
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // Axis indicator axis trigger effective
        type: "shadow", // The default is a straight line, optionally: 'line' | 'shadow'
      },
    },

    // Add legend
    legend: {
      data: [
        "Direct access",
        "Email marketing",
        "Advertising alliance",
        "Video ads",
        "Search Engine",
      ],
    },

    // Add custom colors
    color: ["#1e88e5", "#00acc1", "#212529", "#fc4b6c", "#dadada"],

    // Horizontal axis
    xAxis: [
      {
        type: "value",
      },
    ],

    // Vertical axis
    yAxis: [
      {
        type: "category",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
    ],

    // Add series
    series: [
      {
        name: "Direct access",
        type: "bar",
        stack: "Total",
        itemStyle: {
          normal: { label: { show: true, position: "insideRight" } },
        },
        data: [320, 302, 301, 334, 390, 330, 320],
      },
      {
        name: "Email marketing",
        type: "bar",
        stack: "Total",
        itemStyle: {
          normal: { label: { show: true, position: "insideRight" } },
        },
        data: [120, 132, 101, 134, 90, 230, 210],
      },
      {
        name: "Advertising alliance",
        type: "bar",
        stack: "Total",
        itemStyle: {
          normal: { label: { show: true, position: "insideRight" } },
        },
        data: [220, 182, 191, 234, 290, 330, 310],
      },
      {
        name: "Video ads",
        type: "bar",
        stack: "Total",
        itemStyle: {
          normal: { label: { show: true, position: "insideRight" } },
        },
        data: [150, 212, 201, 154, 190, 330, 410],
      },
      {
        name: "Search Engine",
        type: "bar",
        stack: "Total",
        itemStyle: {
          normal: { label: { show: true, position: "insideRight" } },
        },
        data: [820, 832, 901, 934, 1290, 1330, 1320],
      },
    ],
  };
  // use configuration item and data specified to show chart
  stackedChart.setOption(option);

  //***************************
  // Stacked chart
  //***************************

  //***************************
  // Stacked Area chart
  //***************************
  var stackedbarcolumnChart = echarts.init(
    document.getElementById("stacked-column")
  );
  var option = {
    // Setup grid
    grid: {
      x: 40,
      x2: 40,
      y: 45,
      y2: 25,
    },

    // Add tooltip
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // Axis indicator axis trigger effective
        type: "shadow", // The default is a straight line, optionally: 'line' | 'shadow'
      },
    },

    // Add legend
    legend: {
      data: ["Video", "Search engine", "Google", "Safari", "Opera", "Firefox"],
    },

    // Add custom colors
    color: ["#1e88e5", "#00acc1", "#212529", "#fc4b6c", "#dadada"],

    // Enable drag recalculate
    calculable: true,

    // Horizontal axis
    xAxis: [
      {
        type: "category",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
    ],

    // Vertical axis
    yAxis: [
      {
        type: "value",
      },
    ],

    // Add series
    series: [
      {
        name: "Video",
        type: "bar",
        stack: "advertising",
        data: [150, 232, 201, 154, 190, 330, 410],
      },
      {
        name: "Search engine",
        type: "bar",
        data: [862, 1018, 964, 1026, 1679, 1600, 1570],
        markLine: {
          itemStyle: {
            normal: {
              lineStyle: {
                type: "dashed",
              },
            },
          },
          data: [[{ type: "min" }, { type: "max" }]],
        },
      },
      {
        name: "Google",
        type: "bar",
        barWidth: 12,
        stack: "search engine",
        data: [620, 732, 701, 734, 1090, 1130, 1120],
      },
      {
        name: "Safari",
        type: "bar",
        stack: "search engine",
        data: [120, 132, 101, 134, 290, 230, 220],
      },
      {
        name: "Opera",
        type: "bar",
        stack: "search engine",
        data: [60, 72, 71, 74, 190, 130, 110],
      },
      {
        name: "Firefox",
        type: "bar",
        stack: "search engine",
        data: [62, 82, 91, 84, 109, 110, 120],
      },
    ],
    // Add series
  };
  stackedbarcolumnChart.setOption(option);

  // ------------------------------
  // Basic line chart
  // ------------------------------
  // based on prepared DOM, initialize echarts instance
  var barbasicChart = echarts.init(document.getElementById("bar-basic"));

  var option = {
    // Setup grid
    grid: {
      x: 60,
      x2: 40,
      y: 45,
      y2: 25,
    },

    // Add tooltip
    tooltip: {
      trigger: "axis",
    },

    // Add legend
    legend: {
      data: ["2017", "2018"],
    },

    // Add custom colors
    color: ["#1e88e5", "#00acc1"],

    // Horizontal axis
    xAxis: [
      {
        type: "value",
        boundaryGap: [0, 0.01],
      },
    ],

    // Vertical axis
    yAxis: [
      {
        type: "category",
        data: ["Apple", "Samsung", "HTC", "Nokia", "Sony", "LG"],
      },
    ],

    // Add series
    series: [
      {
        name: "2017",
        type: "bar",
        data: [600, 450, 350, 268, 474, 315],
      },
      {
        name: "2018",
        type: "bar",
        data: [780, 689, 468, 174, 436, 482],
      },
    ],
  };
  // use configuration item and data specified to show chart
  barbasicChart.setOption(option);

  //------------------------------------------------------
  // Resize chart on menu width change and window resize
  //------------------------------------------------------
  $(function () {
    // Resize chart on menu width change and window resize
    $(window).on("resize", resize);
    $(".sidebartoggler").on("click", resize);

    // Resize function
    function resize() {
      setTimeout(function () {
        // Resize chart
        myChart.resize();
        stackedChart.resize();
        stackedbarcolumnChart.resize();
        barbasicChart.resize();
      }, 200);
    }
  });
});
