<svg xmlns="http://www.w3.org/2000/svg" height="480" width="640" version="1">
  <defs>
    <clipPath id="a">
      <path fill-opacity=".67" d="M0 0h682.67v512H0z"/>
    </clipPath>
  </defs>
  <g clip-path="url(#a)" transform="scale(.94)">
    <g fill-rule="evenodd" stroke-width="1pt">
      <path fill="#3e9a00" d="M0 0h767.975v171.886H0z"/>
      <path fill="#fff" d="M0 171.886h767.975v168.222H0z"/>
      <path fill="#e32118" d="M0 340.108h767.975v171.886H0z"/>
      <path d="M0 0v511.984l255.994-255.992L0 0z" fill="#5567e4"/>
    </g>
    <path d="M283.46 148.82v442.91c0 35.433 17.716 70.866 53.15 70.866h70.865c17.717 0 35.433 35.433 35.433 35.433s16.938-35.434 35.433-35.434h70.868c35.433 0 53.15-35.433 53.15-70.866V148.82h-318.9z" fill-rule="evenodd" transform="matrix(.24 0 0 .2 269.51 179.77)" stroke="#000" stroke-width="3.791" fill="#ccc"/>
    <path d="M215.38 731.79c-18.02 0-35.738-17.717-35.738-35.433v-88.583c15.502 15.502 88.583 70.866 70.867 88.583-17.718 17.716-35.738 0-35.434 0s.304 35.433.304 35.433z" fill-rule="evenodd" transform="matrix(.24 0 0 .2 267.81 160.36)" stroke="#000" stroke-width="1pt" fill="#ccc"/>
    <path d="M319.345 289.56c4.245-3.286 4.245 3.288 8.49 0l-4.245 19.724c-4.118-.618-4.26 0-6.638-3.287 0-3.288 0-13.792 2.393-16.436z" fill-rule="evenodd" fill="#ccc"/>
    <path d="M215.38 731.79c-18.02 0-35.738-17.717-35.738-35.433v-88.583c15.502 15.502 88.583 70.866 70.867 88.583-17.718 17.716-35.738 0-35.434 0s.304 35.433.304 35.433z" fill-rule="evenodd" transform="matrix(-.24 0 0 .2 481.9 160.36)" stroke="#000" stroke-width="1pt" fill="#ccc"/>
    <path d="M429.71 289.56c-4.245-3.286-4.245 3.288-8.49 0l4.245 19.724c4.118-.618 4.26 0 6.638-3.287 0-3.288 0-13.792-2.394-16.436z" fill-rule="evenodd" fill="#ccc"/>
    <path d="M513.78 1140.9c0-17.71-35.433-53.14-35.433-70.86s35.433 17.72 106.3 17.72c70.866 0 106.3-88.588 88.582-106.3 17.716 35.434 17.716 70.864 17.716 88.584 0 35.43-35.433 70.86-106.3 70.86H513.78z" fill-rule="evenodd" transform="matrix(.24 0 0 .2 268.41 107.46)" stroke="#000" stroke-width="1pt" fill="#ccc"/>
    <path d="M513.78 1140.9c0-17.71-35.433-53.14-35.433-70.86s35.433 17.72 106.3 17.72c70.866 0 106.3-88.588 88.582-106.3 17.716 35.434 17.716 70.864 17.716 88.584 0 35.43-35.433 70.86-106.3 70.86H513.78z" fill-rule="evenodd" transform="matrix(-.24 0 0 .2 480.65 107.46)" stroke="#000" stroke-width="1pt" fill="#ccc"/>
    <path d="M357.548 315.853c0-3.287 8.49-6.574 8.49-9.862v16.436h-8.49v-6.573zm33.957 0c0-3.287-8.49-6.574-8.49-9.862v16.436h8.49v-6.573z" fill-rule="evenodd" fill="#ccc"/>
    <path d="M371.17 1034.6c0 17.71 141.73 17.71 141.73 0v70.87c0 17.71-141.73 17.71-141.73 0v-70.87z" fill-rule="evenodd" transform="matrix(.24 0 0 .2 268.62 123.89)" stroke="#000" stroke-width="1pt" fill="#ccc"/>
    <path d="M249.63 696.36c-17.717 17.716-17.717-17.717-35.433 0-17.717 17.716 17.717 106.3 88.583 106.3s106.3-35.433 106.3-17.717c0 17.717-35.433 35.433-35.433 53.15s141.73 17.717 141.73 0-35.433-35.433-35.433-53.15c0-17.716 35.433 17.717 106.3 17.717 70.866 0 106.3-88.583 88.583-106.3-17.716-17.717-17.716 17.716-35.433 0" transform="matrix(.24 0 0 .2 268.02 160.36)" stroke="#000" stroke-width="1.25" fill="none"/>
    <path d="M426.836 313.608l-4.368-7.285.762-.308 4.368 7.285-.762.308zm-2.734-1.585l.913-.01c.236.874.246 1.598.028 2.172-.217.57-.636.958-1.256 1.164-.642.213-1.245.22-1.81.027-.564-.2-1.106-.577-1.625-1.133a7.987 7.987 0 0 1-1.306-1.915c-.385-.785-.603-1.508-.654-2.168-.05-.665.07-1.22.357-1.666a2.24 2.24 0 0 1 1.22-.937 2.46 2.46 0 0 1 1.785.075c.595.25 1.143.694 1.644 1.336l-.663.49c-.407-.503-.803-.835-1.19-.998a1.619 1.619 0 0 0-1.174-.045c-.458.152-.773.418-.945.797-.17.38-.2.824-.095 1.334.105.508.28 1.01.523 1.506.315.64.663 1.176 1.045 1.607.384.427.79.707 1.217.84.428.132.823.14 1.186.018.44-.146.735-.432.883-.855.148-.424.12-.97-.084-1.64zm-5.332 5.021l-2.847-7.777.813-.2 2.847 7.776-.814.2zm-5.38.859l-.748-7.137-2.108.15-.1-.956 5.07-.36.1.956-2.116.15.748 7.137-.846.06zm-8.145-2.163l.802-.086c.037.405.125.74.262 1.002.14.26.356.47.647.63.293.16.62.24.986.24.324 0 .61-.06.858-.183.25-.122.434-.288.554-.5a1.38 1.38 0 0 0 .184-.697c0-.254-.06-.477-.176-.665-.117-.19-.31-.35-.578-.48-.173-.086-.553-.216-1.143-.394-.59-.18-1.002-.35-1.24-.51a1.982 1.982 0 0 1-.687-.753 2.276 2.276 0 0 1-.222-1.014c0-.41.092-.793.276-1.147.184-.36.453-.63.806-.815a2.511 2.511 0 0 1 1.178-.277c.477 0 .895.1 1.258.294.365.193.645.477.84.854.196.376.3.803.316 1.28l-.815.077c-.045-.513-.193-.9-.448-1.163-.25-.263-.623-.393-1.116-.393-.515 0-.89.12-1.126.36-.234.235-.35.52-.35.857 0 .292.083.532.25.72.162.19.59.383 1.278.582.692.195 1.167.366 1.423.515.375.217.652.494.83.83.177.332.266.716.266 1.152 0 .432-.098.84-.293 1.225-.196.38-.478.677-.845.892-.365.21-.777.316-1.235.316-.582 0-1.07-.108-1.463-.322a2.264 2.264 0 0 1-.925-.964 3.319 3.319 0 0 1-.35-1.463zm-2.313-5.598l.85.032-.26 4.687c-.046.817-.155 1.46-.328 1.936a2.375 2.375 0 0 1-.857 1.143c-.396.283-.903.415-1.52.392-.602-.023-1.086-.173-1.454-.448-.367-.277-.62-.665-.755-1.166-.136-.504-.18-1.186-.132-2.046l.26-4.687.848.032-.26 4.68c-.038.706-.016 1.228.068 1.568.086.336.25.6.49.79.244.19.548.29.914.305.623.023 1.078-.14 1.364-.487.285-.348.456-1.03.512-2.05l.26-4.68zm-10.487 5.44l.766-.133c.022.62.114 1.045.278 1.273.163.23.39.344.68.344.212 0 .396-.06.55-.183a.958.958 0 0 0 .32-.505c.06-.215.088-.555.088-1.02v-5.595h.85v5.535c0 .68-.066 1.205-.197 1.578-.13.373-.336.658-.62.853-.28.197-.61.295-.99.295-.562 0-.994-.205-1.296-.615-.297-.41-.44-1.02-.43-1.828zm-14.443 13.564v-1.126l3.29-5.87c.233-.418.455-.78.666-1.09h-3.583v-1.08h4.6v1.08l-3.606 6.36-.39.645h4.1v1.08H378zm-46.764-11.262l2.025-9.015.835.126-2.025 9.015-.835-.126zm-9.091-3.552l5.28-8.075.767.34-1.174 7.65 4.145-6.34.718.317-5.28 8.076-.767-.34 1.177-7.657-4.15 6.346-.716-.318zm4.538-9.012l.6.495-4.55 3.75c-.794.65-1.474 1.126-2.043 1.425-.57.3-1.137.458-1.7.478a2.182 2.182 0 0 1-1.497-.51c-.426-.35-.647-.74-.663-1.172-.016-.432.158-.893.523-1.383.367-.494.97-1.084 1.804-1.77l4.552-3.75.6.493-4.546 3.745c-.684.564-1.154 1.01-1.408 1.338-.248.326-.37.635-.368.925.003.292.135.544.394.756.442.362.93.477 1.466.346.536-.133 1.3-.606 2.288-1.42l4.547-3.745zm9.068 13.26l.31-9.163 2.21.05c.5.012.88.064 1.14.158.362.13.67.355.917.678.326.415.56.945.705 1.585.15.638.21 1.365.18 2.18a8.964 8.964 0 0 1-.232 1.85c-.132.534-.293.976-.483 1.326-.19.346-.395.62-.615.818-.217.195-.477.34-.78.44-.303.096-.648.14-1.036.13l-2.317-.05zm.886-1.06l1.37.03c.422.01.756-.04 1-.147.245-.106.443-.26.594-.46.21-.284.38-.666.507-1.147.13-.485.208-1.073.23-1.764.033-.96-.053-1.697-.257-2.215-.202-.522-.458-.873-.767-1.056-.223-.13-.587-.2-1.092-.21l-1.35-.032-.235 7zm5.041 1.12l2.467-9.166h.915l2.628 9.167h-.968l-.75-2.775h-2.684l-.705 2.776h-.902zm1.854-3.763h2.177l-.67-2.538a33.419 33.419 0 0 1-.455-1.902c-.082.587-.198 1.17-.346 1.75l-.706 2.69zm4.834 3.763v-9.166h2.212c.5 0 .88.044 1.144.13.368.123.68.342.94.66.34.407.592.93.76 1.567.17.635.253 1.36.253 2.177 0 .697-.057 1.314-.17 1.852-.115.537-.26.983-.44 1.337-.177.35-.373.628-.586.832a2.12 2.12 0 0 1-.765.455c-.3.104-.642.156-1.03.156h-2.317zm.85-1.08h1.37c.424 0 .756-.056.995-.17.242-.112.435-.27.578-.474.203-.29.36-.674.47-1.157.114-.49.17-1.08.17-1.77 0-.96-.11-1.695-.332-2.21-.22-.515-.486-.86-.802-1.036-.227-.125-.594-.188-1.1-.188h-1.348v7.004zm17.332 11.586v-9.167h2.422c.426 0 .75.03.976.086.315.076.58.22.793.433.214.207.385.5.513.88.132.38.198.796.198 1.25 0 .78-.174 1.44-.52 1.983-.348.538-.976.806-1.884.806H367.4v3.726h-.85zm.85-4.81h1.66c.548 0 .938-.144 1.168-.436.23-.29.346-.702.346-1.232 0-.384-.068-.71-.205-.98-.136-.277-.314-.458-.536-.546-.143-.054-.407-.08-.793-.08h-1.64v3.275zm4.427 4.81l2.467-9.167h.915l2.627 9.166h-.968l-.75-2.776h-2.685l-.705 2.776h-.903zm1.853-3.764h2.177l-.67-2.54a31.814 31.814 0 0 1-.455-1.9c-.083.588-.198 1.17-.347 1.75l-.705 2.69zm55.08-12.49l-4.287-7.838.677-.41 8.057 5.56-.716.434-2.404-1.705-1.987 1.2 1.328 2.356-.667.402zm-1.138-3.594l1.61-.973-2.188-1.566c-.666-.477-1.2-.874-1.604-1.194.33.468.634.948.91 1.44l1.272 2.292z"/>
    <path d="M375.004 251.797c.01 4.945 2.984 19.744-5.652 42.741-.639 1.67 1.06 2.473 2.473 2.826 1.413.353 2.12-1.413 2.12-1.766s.353 2.826 1.765 2.472c1.413-.353 2.826-2.472 2.826-2.472s.707 3.179 2.473 3.179 1.766-3.886 1.766-3.886c0 .353 1.224 3.335 2.637 3.335s3.59-1.314 2.332-3.471c-8.975-15.108-8.502-40.131-8.502-42.957 0-2.826 4.168-7.96 8.313-8.084l-1.601-2.513c-1.044.32-2.292.55-4.412 2.316s-3.36 3.689-3.36 3.689v-12.01h-3.532l.354 10.95s-1.208-1.416-3.015-2.99c-1.71-1.49-4.795-1.38-6.695-1.067l-.18 2.398c2.245.469 5.11-.46 6.522 1.66 1.767 1.765 3.363 3.188 3.368 5.65z" fill-rule="evenodd" stroke="#000" stroke-width=".75435" fill="#682900"/>
    <path d="M310.55 312.21v-.832c0 1.976-.067 1.1.833-2.498.777-2.848 2.407-5.523 4.163-7.493 3.04-1.014 7.44.053 10.823-.833 2.277-3.268 5.827 4.267 5.827-.833 4.343-.31 4.4-4.558 5.828-6.66 4.054-.5 4.963-5.44 8.326-2.496 2.757 1.305.407-.833 4.997-.833 2.713.664 4.13 2.32 6.66.833.62-2.85 1.4-6.268 1.665-9.99-1.177-2.356-.832-7.184-.832-10.824 2.274-3.033 6.262 2.475 9.99 1.664 1.018-3.562 4.146-3.466 2.498-6.66-.308-4.007-1.08-6.503 0-10.823 1.314-3.703 3.228-3.606 6.66-5.827 1.093-3.277-.122-6.03.833-9.99 3.902-.116 3.833-1.133 4.164-4.996 1.26-2.66 2.858-5.356 4.995-7.493 1.967-2.382 4.302-2.147 5.827-5.828 1.308-3.27 1.31-4.75 4.995-5.828 2.696-1.27 4.833-1.41 6.66-3.33 1.733-3.057 3.175-5.487 5.83-9.16 2.63-1.77 15.236 1.666 19.148 1.666 2.118-2.67 5.397-6.86 7.493-8.326 3.587-1.916 4.96-2.497 9.99-2.497 4.288 1.27 6.082 2.795 10.824 1.666 1.932-2.706 3.816-3.965 8.325-2.498 3.433 2.22 5.347 2.124 6.66 5.828 1.947 4.615 1.51 6.192 6.662 6.66 5.18.186 5.71 1.443 9.158 4.163 5.248 2.25 4.995.98 4.995 5.827 3.72 0 4.052-.44 4.163 3.33 2.294.714 4.907 2.72 6.66 4.163 2.094 2.416 2.63 3.93 7.494 4.163 3.59-1.508 4.75-1.496 4.995-5.828 2.75-.982 3.21-3.635 4.162-6.66 2.108-2.885 3.474-3.33 8.325-3.33 4.28 1.772 4.14 3.567 3.33 7.493 2.665.55 3.765.24 4.164 3.33 1.408 2.346 1.04 5.15-.832 6.66 2.132 1.568 3.37 4.256 4.163 7.493-1.41 2.353-.546 5.81 0 8.327 2.913 1.456 4.416 2.81 8.326 4.163 1.983 1.68 6.2 3.44 9.99 4.163 4.482-.606 5.962-.568 9.992 1.665 2.084 2.993 2.235 6.77 4.163 8.326 2.176 1.843 5.82 2.65 7.494 4.995 2.778 2.03 3.594 4.28 7.493 4.994 1.47 2.228 4.634 5.896 5.828 8.326.94 2.694 1.36 7.413 2.498 10.823v11.656c-.158 4.303-.833 6.915-.833 11.655-.74 2.384-1.665 3.3-1.665 6.66-1.582 3.527-2.93 6.49-4.995 9.16-.94 4.234-2.473 6.222-4.163 9.99.052 4.37.832 6.304.832 10.823-1.81 2.69-3.888 4.647-7.494 6.66-2.724 3.188-4.368 4.427-4.995 9.16-1.565 2.704-1.442 2.497-4.994 2.497-4.2 0-7.814.375-11.656.832-3.378 0-4.64.356-6.66-1.665-3.99-1.804-7.346-1.91-11.657-.833-2.325-1.595-3.258-2.498-7.493-2.498-3.16.427-5.965.832-9.99.832-2.496-2.79-3.898-3.33-8.326-3.33-2.356 1.178-7.184.833-10.824.833-3.82-1.043-4.313-2.63-5.828-5.828-2.605-1.236-3.635-3.04-7.493-3.332-3.64 0-8.47.345-10.824-.832-2.723-.327-5.586-1.027-7.493-2.498a22.068 22.068 0 0 0-5.828-5.828c-3.698 0-5.864.355-7.493-1.665-2.732-2.157-4.89-5.564-8.326-7.493-3.062-2.43-5.136-3.916-6.66-8.326 0-3.943.78-4.874-3.33-4.995-2.756 2.813-4.01 3.33-9.16 3.33-3.388 0-6.93-.017-9.157-.83-3.682-2.09-5.03-3.014-9.16-3.332-2.584 1.645-6.007 3.547-9.99 4.163-3.405 1.967-5.437 2.38-6.66 5.83-3.146 2.567-4.99 4.077-9.16 5.827-2.315 2.21-4.525 2.59-5.827 6.66-.792 3.367-1.665 5.37-1.665 9.99.59 4.37.36 6.238-1.665 9.992-1.74 2.485-4.757 3.7-7.493 5.827-4.116.494-4.548.186-7.493-1.665-2.933-.977-6.266-1.78-9.99-2.498-4.497-.866-6.018-1.96-8.327-4.162l8.326 4.162c-4.496-.866-6.017-1.96-8.326-4.162-5.064-3.583-6.063-5.398-11.656-5.828-5.198.06-7.43.318-8.325 4.995 2.96.788.608 2.553-.834 3.33-2.078 1.85-5.41 3.367-8.326 4.162-1.328 1.328-1.072-1.1-3.33-1.665-3.448-1.842-4.89-3.226-8.326-4.163-1.683-2.97-3.584-5.274-5.828-8.326-1.734-2.818-3.382-3.838-5.828-6.66-1.17-1.754-4.16-1.83-5.827-3.33-1-3.002-3.168-8.57-2.497-11.656 1.68-1.586 2.632-7.243 4.995-8.326 3.367-2.525 4.635-.223 9.16 0 4.258 0-5.867-6.71-1.666-6.66 3.92-.666 4.387-2.595 5.827-4.996h9.99z" fill-rule="evenodd" transform="matrix(.2 0 0 .15 285.64 190.05)" stroke="#000" stroke-width="4.464" fill="#009200"/>
    <path d="M362.236 229.77c.176.143.937.5 1.665.5.795.145.969.443 1.166 1.165.435.261.71.333 1.498.333.872.175.865.443 1.499 1m-10.656 6.326c.362.053 1.63.166 2.498.166.666-.384.982-.648 1.498-.999.789 0 1.064.072 1.5.333.42.14 1.41.193 1.83.333 0 .222-.055.167.167.167m-.999-2.664c.013-.005.65-.39 1.166-.5.567-.395 1.189-.588 1.831-.832.424-.092 1.42-.09 1.665-.167m3.496-.999v.167c0-.466-.02-.218.333.666.175.815.39 1.505.666 2.164.491.766.812 1.11 1.499 1.499.588.463 1.032.944 1.665 1.165.44.393 1.021.717 1.665 1 .854 0 1.511.137 2.33.166h.5m.334-16.151v.167c0-.45.011-.221-.166.666-.252.53-.541 1.024-.833 1.498-.783.284-1.075.856-1.332 1.499-.275.446-.701 1.034-1 1.498-.517.642-.724.849-1.665 1.166m11.656-2.998c0 .049-.307.487-.5 1-.33.486-.415 1.371-.499 1.997 0 .724.004 1.512.166 1.998.504.625.632.953 1.499 1.333l-1.498-1.332c.503.624.632.952 1.499 1.332m11.489-.5v.167c0-.522.043-.202-.833.499-.65.6-1.114 1.083-1.665 1.499-.946.25-1.09.236-1.998.166h-1.498m8.326 10.325h-.166c.465 0 .218-.02-.666.333-.739.185-1.576.196-2.165 0-.593-.336-1.191-.611-1.665-1.166-.248-.186-.284-.26-.5-.333m-.666 6.328v-.167c0 .522.043.202-.833-.5-.374-.55-.75-.758-1.498-1.331-.647-.783-.732-1.097-1-1.999-.07-.664-.29-1.346 0-1.831m-8.991 2.996c.362 0 1.712-.032 2.331-.167.484-.328 1.019-.549 1.665-.832.353-.872.654-1.162.666-2.165m-14.82-1.83c-.13-.297-.709-1.487-.999-2.165-.013-.727-.167-1.257-.167-1.998m-21.314 9.491c.168.025.875.08 1.332 0 .727-.372 1.404-.441 2.165-.5.386-.232 1.017-.206 1.332-.499.695-.463.787-.929 1.166-1.665.033-.335.132-.498.166-.833m4.829.999c-.366 0-1.732.025-2.498-.167-.393-.254-.672-.672-.999-.999.222 0 .167.056.167-.166m5.328-3.496c.07-.021.897-.333 1.499-.333.358-.232.952-.447 1.498-.666.296-.16.501-.357.833-.5m-23.978 8.159h.167c-.45 0-.22.01.666-.167 1.036-.064 1.733-.422 2.498-.666a8.022 8.022 0 0 1 1.664-1.165c0-.285-.068-.255.167-.333m37.134-6.827h.167c-.466 0-.218-.02.666.333.703.27 1.339.356 1.998.5.598.146.825.333 1.665.333.838.365 1.02.479 1.665.999.493.233.844.333 1.665.333.436-.242 1.232-.522 1.665-.666.242-.182.259-.11.333-.333" stroke="#006800" stroke-width=".81" fill="none"/>
    <path d="M374.548 272.438v3.768c0 1.203-.111 2.255-.471 3.297 0 1.175-.015 2.31-.471 3.06-.08 1.124-.235 2.096-.235 3.297-.341 1.024-.301 2.41-.471 3.533-.148 1.101-.279 2.412-.471 3.297-.351.736-.95 2.07-1.413 2.59-.058 1.017-.307 1.517 0 2.355.328.984.235 2.17.235 3.296.307.922.236 2.001.236 3.061m4.237-26.846c.072.472.424 2.175.471 3.297v5.18c0 1.26.106 2.37.236 3.533-.017 1.388-.27 1.908-.942 3.061-.133 1.112-.339 2.185-.471 3.297-.328.985-.236 2.17-.236 3.297.281 1.264.741 1.49.942 2.59 0 .672.064 1.013.236 1.413m1.884-17.19c0 .431-.028 2.506.236 3.297 0 1.204.22 2.109.235 3.297-.143 1.056-.29 1.816-.942 2.354-.429.872-.663 1.647-.706 2.826.608.426 1.27 1.616 1.884 2.12.513 1.072.99 1.118 1.177 2.354.492.547-.347.831 0 1.178.382.51.137.293 0 .706" stroke="#000" stroke-width=".2pt" fill="none"/>
    <path fill-rule="evenodd" transform="matrix(.16 0 0 .15 369.58 198.54)" stroke="#000" stroke-width="3.125" fill="#ffd100" d="M93.248-106.57l15.982 25.596 30.16 1.046-14.17 26.642 14.17 26.642-30.16 1.045L93.248 0 77.262-25.6l-30.16-1.044 14.175-26.642-14.175-26.642 30.16-1.046z"/>
    <path fill-rule="evenodd" transform="matrix(.16 0 0 .15 349.7 198.54)" stroke="#000" stroke-width="3.125" fill="#ffd100" d="M93.248-106.57l15.982 25.596 30.16 1.046-14.17 26.642 14.17 26.642-30.16 1.045L93.248 0 77.262-25.6l-30.16-1.044 14.175-26.642-14.175-26.642 30.16-1.046z"/>
    <path fill-rule="evenodd" transform="matrix(.16 -.02 .02 .15 332.67 203.68)" stroke="#000" stroke-width="3.125" fill="#ffd100" d="M93.248-106.57l15.982 25.596 30.16 1.046-14.17 26.642 14.17 26.642-30.16 1.045L93.248 0 77.262-25.6l-30.16-1.044 14.175-26.642-14.175-26.642 30.16-1.046z"/>
    <path fill-rule="evenodd" transform="matrix(-.16 -.02 -.02 .15 418.3 203.19)" stroke="#000" stroke-width="3.125" fill="#ffd100" d="M93.248-106.57l15.982 25.596 30.16 1.046-14.17 26.642 14.17 26.642-30.16 1.045L93.248 0 77.262-25.6l-30.16-1.044 14.175-26.642-14.175-26.642 30.16-1.046z"/>
    <path fill-rule="evenodd" transform="matrix(-.15 -.05 -.05 .14 433.58 213.41)" stroke="#000" stroke-width="3.125" fill="#ffd100" d="M93.248-106.57l15.982 25.596 30.16 1.046-14.17 26.642 14.17 26.642-30.16 1.045L93.248 0 77.262-25.6l-30.16-1.044 14.175-26.642-14.175-26.642 30.16-1.046z"/>
    <path fill-rule="evenodd" transform="matrix(.15 -.05 .05 .14 316.85 212.43)" stroke="#000" stroke-width="3.125" fill="#ffd100" d="M93.248-106.57l15.982 25.596 30.16 1.046-14.17 26.642 14.17 26.642-30.16 1.045L93.248 0 77.262-25.6l-30.16-1.044 14.175-26.642-14.175-26.642 30.16-1.046z"/>
  </g>
</svg>
